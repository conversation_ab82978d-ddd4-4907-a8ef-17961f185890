// Production API Route for Agent Analytics and Performance Tracking
// Comprehensive metrics, A/B testing, and optimization insights

import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/database";
import { verifyToken, hasPermission } from "@/lib/auth-context";
import { getAPXClient } from "@/lib/apix-client";
import { getSessionManager } from "@/lib/session-manager";

interface AnalyticsQuery {
  agentId?: string;
  timeRange?: "1h" | "24h" | "7d" | "30d" | "90d";
  metrics?: string[];
  groupBy?: "hour" | "day" | "week";
  includeComparison?: boolean;
}

interface AgentMetrics {
  agentId: string;
  agentName: string;
  timeRange: string;
  totalExecutions: number;
  successRate: number;
  avgLatency: number;
  avgCost: number;
  totalCost: number;
  userSatisfaction: number;
  errorRate: number;
  topErrors: Array<{
    error: string;
    count: number;
    percentage: number;
  }>;
  performanceTrend: Array<{
    timestamp: string;
    executions: number;
    latency: number;
    cost: number;
    successRate: number;
  }>;
  toolUsage?: Array<{
    toolName: string;
    callCount: number;
    successRate: number;
    avgLatency: number;
  }>;
  knowledgeUsage?: {
    searchCount: number;
    avgRelevanceScore: number;
    topSources: Array<{
      source: string;
      hitCount: number;
    }>;
  };
  abTestResults?: Array<{
    testId: string;
    variant: string;
    trafficPercentage: number;
    conversionRate: number;
    statisticalSignificance: number;
    isWinner: boolean;
  }>;
}

// GET /api/agents/analytics - Get agent performance analytics
export async function GET(request: NextRequest) {
  try {
    const authHeader = request.headers.get("authorization");
    if (!authHeader?.startsWith("Bearer ")) {
      return NextResponse.json(
        { error: "Missing or invalid authorization header" },
        { status: 401 },
      );
    }

    const token = authHeader.substring(7);
    const payload = await verifyToken(token);
    if (!payload) {
      return NextResponse.json(
        { error: "Invalid or expired token" },
        { status: 401 },
      );
    }

    if (
      !hasPermission(payload.role, payload.permissions, "analytics", "read")
    ) {
      return NextResponse.json(
        { error: "Insufficient permissions to read analytics" },
        { status: 403 },
      );
    }

    const { searchParams } = new URL(request.url);
    const query: AnalyticsQuery = {
      agentId: searchParams.get("agentId") || undefined,
      timeRange: (searchParams.get("timeRange") as any) || "24h",
      metrics: searchParams.get("metrics")?.split(",") || undefined,
      groupBy: (searchParams.get("groupBy") as any) || "hour",
      includeComparison: searchParams.get("includeComparison") === "true",
    };

    // If specific agent requested, verify access
    if (query.agentId) {
      const agent = await db.getAgent(query.agentId, payload.org);
      if (!agent) {
        return NextResponse.json({ error: "Agent not found" }, { status: 404 });
      }
    }

    // Get analytics data
    const analytics = await getAgentAnalytics(query, payload.org);

    // Create session for analytics access
    const sessionManager = getSessionManager(payload.org);
    const sessionId = await sessionManager.createSession(
      "analytics",
      "user",
      {
        action: "view_analytics",
        query,
        resultsCount: analytics.length,
      },
      {
        tags: ["api", "analytics", "agents", "performance"],
        conversationId: `api-analytics-${Date.now()}`,
      },
    );

    // Send real-time event
    try {
      const apixClient = getAPXClient(payload.org, payload.sub, token);
      await apixClient.sendEvent({
        id: `analytics-viewed-${Date.now()}`,
        type: "analytics_viewed",
        module: "analytics",
        organizationId: payload.org,
        userId: payload.sub,
        timestamp: new Date().toISOString(),
        data: {
          query,
          agentCount: analytics.length,
          sessionId,
        },
        version: 1,
      });
    } catch (apixError) {
      console.warn("Failed to send APIX event:", apixError);
    }

    return NextResponse.json({
      success: true,
      data: {
        analytics,
        query,
        generatedAt: new Date().toISOString(),
        sessionId,
      },
    });
  } catch (error) {
    console.error("Error fetching agent analytics:", error);
    return NextResponse.json(
      {
        error: "Internal server error",
        message: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 },
    );
  }
}

// POST /api/agents/analytics - Create A/B test or performance benchmark
export async function POST(request: NextRequest) {
  try {
    const authHeader = request.headers.get("authorization");
    if (!authHeader?.startsWith("Bearer ")) {
      return NextResponse.json(
        { error: "Missing or invalid authorization header" },
        { status: 401 },
      );
    }

    const token = authHeader.substring(7);
    const payload = await verifyToken(token);
    if (!payload) {
      return NextResponse.json(
        { error: "Invalid or expired token" },
        { status: 401 },
      );
    }

    if (!hasPermission(payload.role, payload.permissions, "agents", "update")) {
      return NextResponse.json(
        { error: "Insufficient permissions to create A/B tests" },
        { status: 403 },
      );
    }

    const body = await request.json();

    if (body.type === "ab_test") {
      const abTest = await createABTest(body, payload.org, payload.sub);

      // Send real-time event
      try {
        const apixClient = getAPXClient(payload.org, payload.sub, token);
        await apixClient.sendEvent({
          id: `ab-test-created-${abTest.id}`,
          type: "ab_test_created",
          module: "analytics",
          organizationId: payload.org,
          userId: payload.sub,
          timestamp: new Date().toISOString(),
          data: {
            testId: abTest.id,
            agentIds: body.agentIds,
            trafficSplit: body.trafficSplit,
          },
          version: 1,
        });
      } catch (apixError) {
        console.warn("Failed to send APIX event:", apixError);
      }

      return NextResponse.json(
        {
          success: true,
          data: { abTest },
        },
        { status: 201 },
      );
    }

    return NextResponse.json(
      { error: "Invalid request type" },
      { status: 400 },
    );
  } catch (error) {
    console.error("Error creating analytics resource:", error);
    return NextResponse.json(
      {
        error: "Internal server error",
        message: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 },
    );
  }
}

// Helper function to get agent analytics
async function getAgentAnalytics(
  query: AnalyticsQuery,
  organizationId: string,
): Promise<AgentMetrics[]> {
  // Mock implementation - in production, this would query analytics database
  const mockAnalytics: AgentMetrics[] = [
    {
      agentId: "agent-1",
      agentName: "Customer Support Agent",
      timeRange: query.timeRange || "24h",
      totalExecutions: 1247,
      successRate: 0.956,
      avgLatency: 1234,
      avgCost: 0.0234,
      totalCost: 29.18,
      userSatisfaction: 4.7,
      errorRate: 0.044,
      topErrors: [
        { error: "Rate limit exceeded", count: 23, percentage: 52.3 },
        { error: "Invalid input format", count: 12, percentage: 27.3 },
        { error: "Provider timeout", count: 9, percentage: 20.4 },
      ],
      performanceTrend: generatePerformanceTrend(query.timeRange || "24h"),
      toolUsage: [
        {
          toolName: "search_knowledge",
          callCount: 234,
          successRate: 0.98,
          avgLatency: 456,
        },
        {
          toolName: "send_email",
          callCount: 89,
          successRate: 0.94,
          avgLatency: 1200,
        },
      ],
      knowledgeUsage: {
        searchCount: 567,
        avgRelevanceScore: 0.87,
        topSources: [
          { source: "Product Documentation", hitCount: 234 },
          { source: "FAQ Database", hitCount: 189 },
          { source: "Support Tickets", hitCount: 144 },
        ],
      },
      abTestResults: [
        {
          testId: "ab-test-1",
          variant: "A",
          trafficPercentage: 50,
          conversionRate: 0.12,
          statisticalSignificance: 0.95,
          isWinner: true,
        },
        {
          testId: "ab-test-1",
          variant: "B",
          trafficPercentage: 50,
          conversionRate: 0.09,
          statisticalSignificance: 0.95,
          isWinner: false,
        },
      ],
    },
  ];

  if (query.agentId) {
    return mockAnalytics.filter((a) => a.agentId === query.agentId);
  }

  return mockAnalytics;
}

// Helper function to generate performance trend data
function generatePerformanceTrend(timeRange: string) {
  const points =
    timeRange === "1h"
      ? 12
      : timeRange === "24h"
        ? 24
        : timeRange === "7d"
          ? 7
          : 30;
  const trend = [];

  for (let i = 0; i < points; i++) {
    const timestamp = new Date(
      Date.now() - (points - i) * getTimeInterval(timeRange),
    );
    trend.push({
      timestamp: timestamp.toISOString(),
      executions: Math.floor(Math.random() * 100) + 20,
      latency: Math.floor(Math.random() * 500) + 800,
      cost: Math.random() * 0.05 + 0.01,
      successRate: Math.random() * 0.1 + 0.9,
    });
  }

  return trend;
}

function getTimeInterval(timeRange: string): number {
  switch (timeRange) {
    case "1h":
      return 5 * 60 * 1000; // 5 minutes
    case "24h":
      return 60 * 60 * 1000; // 1 hour
    case "7d":
      return 24 * 60 * 60 * 1000; // 1 day
    case "30d":
      return 24 * 60 * 60 * 1000; // 1 day
    default:
      return 60 * 60 * 1000;
  }
}

// Helper function to create A/B test
async function createABTest(data: any, organizationId: string, userId: string) {
  // Mock implementation - in production, this would create in database
  return {
    id: `ab-test-${Date.now()}`,
    name: data.name,
    description: data.description,
    agentIds: data.agentIds,
    trafficSplit: data.trafficSplit,
    status: "active",
    createdAt: new Date().toISOString(),
    createdBy: userId,
    organizationId,
  };
}
