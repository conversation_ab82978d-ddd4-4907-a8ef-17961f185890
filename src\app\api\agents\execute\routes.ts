// Production API Route for Agent Execution with Tool Integration
// Real-time execution with tool calling, knowledge integration, and streaming

import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/database";
import { aiProviders } from "@/lib/ai-providers";
import { verifyToken, hasPermission } from "@/lib/auth-context";
import { getAPXClient } from "@/lib/apix-client";
import { getSessionManager } from "@/lib/session-manager";

interface ExecuteAgentRequest {
  agentId: string;
  input: string;
  context?: {
    conversationId?: string;
    sessionId?: string;
    toolsEnabled?: boolean;
    knowledgeEnabled?: boolean;
    streamResponse?: boolean;
  };
  tools?: Array<{
    id: string;
    name: string;
    description: string;
    parameters: Record<string, any>;
  }>;
  knowledgeSources?: Array<{
    id: string;
    name: string;
    type: string;
  }>;
  metadata?: Record<string, any>;
}

interface ToolCall {
  id: string;
  name: string;
  parameters: Record<string, any>;
  result?: any;
  error?: string;
  executionTime?: number;
}

interface KnowledgeResult {
  id: string;
  content: string;
  source: string;
  relevanceScore: number;
  metadata?: Record<string, any>;
}

// POST /api/agents/execute - Execute agent with tool and knowledge integration
export async function POST(request: NextRequest) {
  const startTime = Date.now();
  let executionId: string | null = null;
  let sessionId: string | null = null;
  let conversationSessionId: string | null = null;

  try {
    // Extract and verify JWT token
    const authHeader = request.headers.get("authorization");
    if (!authHeader?.startsWith("Bearer ")) {
      return NextResponse.json(
        { success: false, error: "Missing or invalid authorization header" },
        { status: 401 },
      );
    }

    const token = authHeader.substring(7);
    const payload = await verifyToken(token);
    if (!payload) {
      return NextResponse.json(
        { success: false, error: "Invalid or expired token" },
        { status: 401 },
      );
    }

    // Check permissions
    if (
      !hasPermission(payload.role, payload.permissions, "agents", "execute")
    ) {
      return NextResponse.json(
        { success: false, error: "Insufficient permissions to execute agents" },
        { status: 403 },
      );
    }

    // Parse and validate request body
    const body: ExecuteAgentRequest = await request.json();

    if (!body.agentId || !body.input?.trim()) {
      return NextResponse.json(
        { success: false, error: "Agent ID and input are required" },
        { status: 400 },
      );
    }

    // Get agent configuration
    const agent = await db.getAgent(body.agentId, payload.org);
    if (!agent) {
      return NextResponse.json(
        { success: false, error: "Agent not found" },
        { status: 404 },
      );
    }

    // Validate provider availability
    if (!aiProviders.isProviderAvailable(agent.provider)) {
      return NextResponse.json(
        {
          success: false,
          error: `Provider ${agent.provider} is not available`,
        },
        { status: 503 },
      );
    }

    // Create session manager and sessions
    const sessionManager = getSessionManager(payload.org);

    // Create main execution session
    sessionId = await sessionManager.createSession(
      "agents",
      "agent",
      {
        action: "execute",
        agentId: body.agentId,
        agent,
        input: body.input,
        context: body.context,
        startTime: new Date().toISOString(),
        toolsEnabled: body.context?.toolsEnabled || false,
        knowledgeEnabled: body.context?.knowledgeEnabled || false,
      },
      {
        tags: ["api", "agents", "execute", "production"],
        conversationId: body.context?.conversationId || `exec-${Date.now()}`,
        parentSessionId: body.context?.sessionId,
      },
    );

    // Create conversation memory session if not provided
    if (!body.context?.sessionId) {
      conversationSessionId = await sessionManager.createSession(
        "sessions",
        "user",
        {
          type: "conversation",
          agentId: body.agentId,
          userId: payload.sub,
          messages: [],
          context: {},
        },
        {
          tags: ["conversation", "memory"],
          conversationId: body.context?.conversationId || `conv-${Date.now()}`,
        },
      );
    }

    // Create execution record
    const executionData = {
      agentId: body.agentId,
      organizationId: payload.org,
      userId: payload.sub,
      input: body.input,
      output: "",
      status: "running" as const,
      cost: 0,
      tokens: {
        input: 0,
        output: 0,
        total: 0,
      },
      metadata: {
        ...body.metadata,
        sessionId,
        conversationSessionId: conversationSessionId || body.context?.sessionId,
        toolsEnabled: body.context?.toolsEnabled || false,
        knowledgeEnabled: body.context?.knowledgeEnabled || false,
        streamResponse: body.context?.streamResponse || false,
        availableTools: body.tools || [],
        knowledgeSources: body.knowledgeSources || [],
      },
    };

    const execution = await db.createExecution(executionData);
    executionId = execution.id;

    // Send execution start event via APIX
    const apixClient = getAPXClient(payload.org, payload.sub, token);
    await apixClient.sendEvent({
      id: `agent-execute-start-${executionId}`,
      type: "execution_started",
      module: "agents",
      organizationId: payload.org,
      userId: payload.sub,
      timestamp: new Date().toISOString(),
      data: {
        executionId,
        agentId: body.agentId,
        input: body.input,
        sessionId,
        toolsEnabled: body.context?.toolsEnabled || false,
        knowledgeEnabled: body.context?.knowledgeEnabled || false,
      },
      version: 1,
    });

    // Retrieve conversation history for context
    let conversationHistory: Array<{ role: string; content: string }> = [];
    if (body.context?.sessionId || conversationSessionId) {
      try {
        const convSession = await sessionManager.getSession(
          body.context?.sessionId || conversationSessionId!,
        );
        if (convSession?.data?.messages) {
          conversationHistory = convSession.data.messages.slice(-10); // Last 10 messages
        }
      } catch (error) {
        console.warn("Failed to retrieve conversation history:", error);
      }
    }

    // Knowledge integration - search relevant knowledge
    let knowledgeResults: KnowledgeResult[] = [];
    if (body.context?.knowledgeEnabled && body.knowledgeSources?.length) {
      try {
        // Simulate knowledge search - in production, integrate with vector database
        knowledgeResults = await searchKnowledge(
          body.input,
          body.knowledgeSources,
          payload.org,
        );

        // Send knowledge search event
        await apixClient.sendEvent({
          id: `knowledge-search-${executionId}`,
          type: "knowledge_searched",
          module: "knowledge",
          organizationId: payload.org,
          userId: payload.sub,
          timestamp: new Date().toISOString(),
          data: {
            executionId,
            query: body.input,
            sources: body.knowledgeSources,
            results: knowledgeResults.length,
            relevantContent: knowledgeResults.slice(0, 3),
          },
          version: 1,
        });
      } catch (error) {
        console.warn("Knowledge search failed:", error);
      }
    }

    // Prepare enhanced system prompt with knowledge context
    let enhancedSystemPrompt = agent.systemPrompt;
    if (knowledgeResults.length > 0) {
      const knowledgeContext = knowledgeResults
        .slice(0, 3)
        .map((result) => `Source: ${result.source}\nContent: ${result.content}`)
        .join("\n\n");

      enhancedSystemPrompt += `\n\nRelevant Knowledge Context:\n${knowledgeContext}\n\nWhen answering, cite sources using [Source: ${knowledgeResults.map((r) => r.source).join(", ")}] format.`;
    }

    // Prepare tool definitions for AI provider
    let toolDefinitions: any[] = [];
    if (body.context?.toolsEnabled && body.tools?.length) {
      toolDefinitions = body.tools.map((tool) => ({
        type: "function",
        function: {
          name: tool.name,
          description: tool.description,
          parameters: tool.parameters,
        },
      }));
    }

    // Prepare messages with conversation history
    const messages = [
      {
        role: "system" as const,
        content: enhancedSystemPrompt,
      },
      ...conversationHistory,
      {
        role: "user" as const,
        content: body.input,
      },
    ];

    // Execute with AI provider
    const aiRequest = {
      provider: agent.provider,
      model: agent.model,
      messages,
      temperature: agent.temperature,
      maxTokens: agent.maxTokens,
      tools: toolDefinitions.length > 0 ? toolDefinitions : undefined,
      organizationId: payload.org,
      userId: payload.sub,
      stream: body.context?.streamResponse || false,
    };

    let aiResponse = await aiProviders.generateResponse(aiRequest);
    let toolCalls: ToolCall[] = [];
    let totalCost = aiResponse.cost;

    // Handle tool calls if present
    if (aiResponse.toolCalls && aiResponse.toolCalls.length > 0) {
      for (const toolCall of aiResponse.toolCalls) {
        const toolStartTime = Date.now();

        try {
          // Execute tool
          const toolResult = await executeToolCall(
            toolCall,
            body.tools || [],
            payload.org,
            payload.sub,
            executionId,
          );

          const toolExecutionTime = Date.now() - toolStartTime;

          toolCalls.push({
            id: toolCall.id,
            name: toolCall.function.name,
            parameters: toolCall.function.arguments,
            result: toolResult.result,
            executionTime: toolExecutionTime,
          });

          // Send tool execution event
          await apixClient.sendEvent({
            id: `tool-executed-${toolCall.id}`,
            type: "tool_executed",
            module: "tools",
            organizationId: payload.org,
            userId: payload.sub,
            timestamp: new Date().toISOString(),
            data: {
              executionId,
              toolId: toolCall.function.name,
              parameters: toolCall.function.arguments,
              result: toolResult.result,
              executionTime: toolExecutionTime,
              success: true,
            },
            version: 1,
          });

          totalCost += toolResult.cost || 0;
        } catch (toolError) {
          console.error(
            `Tool execution failed for ${toolCall.function.name}:`,
            toolError,
          );

          toolCalls.push({
            id: toolCall.id,
            name: toolCall.function.name,
            parameters: toolCall.function.arguments,
            error:
              toolError instanceof Error
                ? toolError.message
                : "Tool execution failed",
            executionTime: Date.now() - toolStartTime,
          });

          // Send tool error event
          await apixClient.sendEvent({
            id: `tool-error-${toolCall.id}`,
            type: "tool_error",
            module: "tools",
            organizationId: payload.org,
            userId: payload.sub,
            timestamp: new Date().toISOString(),
            data: {
              executionId,
              toolId: toolCall.function.name,
              error:
                toolError instanceof Error
                  ? toolError.message
                  : "Tool execution failed",
              success: false,
            },
            version: 1,
          });
        }
      }

      // If tools were called, make a follow-up request with tool results
      if (toolCalls.some((tc) => tc.result)) {
        const toolMessages = toolCalls.map((tc) => ({
          role: "tool" as const,
          content: JSON.stringify(tc.result || tc.error),
          tool_call_id: tc.id,
        }));

        const followUpRequest = {
          ...aiRequest,
          messages: [...messages, ...toolMessages],
          tools: undefined, // Don't allow recursive tool calls
        };

        const followUpResponse =
          await aiProviders.generateResponse(followUpRequest);
        aiResponse = {
          ...followUpResponse,
          cost: aiResponse.cost + followUpResponse.cost,
          usage: {
            promptTokens:
              aiResponse.usage.promptTokens +
              followUpResponse.usage.promptTokens,
            completionTokens:
              aiResponse.usage.completionTokens +
              followUpResponse.usage.completionTokens,
            totalTokens:
              aiResponse.usage.totalTokens + followUpResponse.usage.totalTokens,
          },
        };
        totalCost = aiResponse.cost;
      }
    }

    const totalLatency = Date.now() - startTime;

    // Update execution record with results
    const updatedExecution = await db.updateExecution(
      executionId,
      payload.org,
      {
        output: aiResponse.content,
        status: "completed",
        duration: totalLatency,
        cost: totalCost,
        tokens: {
          input: aiResponse.usage.promptTokens,
          output: aiResponse.usage.completionTokens,
          total: aiResponse.usage.totalTokens,
        },
        metadata: {
          ...executionData.metadata,
          finishReason: aiResponse.finishReason,
          providerLatency: aiResponse.latency,
          totalLatency,
          toolCalls,
          knowledgeResults: knowledgeResults.length,
          knowledgeSources: knowledgeResults.map((kr) => kr.source),
        },
      },
    );

    // Update conversation session with new messages
    const activeSessionId = body.context?.sessionId || conversationSessionId;
    if (activeSessionId) {
      try {
        const currentSession = await sessionManager.getSession(activeSessionId);
        const updatedMessages = [
          ...(currentSession?.data?.messages || []),
          {
            role: "user",
            content: body.input,
            timestamp: new Date().toISOString(),
          },
          {
            role: "assistant",
            content: aiResponse.content,
            timestamp: new Date().toISOString(),
          },
        ];

        await sessionManager.updateSession(activeSessionId, {
          messages: updatedMessages,
          lastActivity: new Date().toISOString(),
          totalCost: (currentSession?.data?.totalCost || 0) + totalCost,
          messageCount: updatedMessages.length,
        });
      } catch (sessionError) {
        console.warn("Failed to update conversation session:", sessionError);
      }
    }

    // Update main execution session
    await sessionManager.updateSession(sessionId, {
      output: aiResponse.content,
      cost: totalCost,
      latency: totalLatency,
      tokens: aiResponse.usage,
      endTime: new Date().toISOString(),
      success: true,
      toolCalls,
      knowledgeResults: knowledgeResults.length,
    });

    // Update agent usage statistics
    await db.updateAgentUsage(body.agentId, payload.org, totalCost);

    // Send execution completion event
    await apixClient.sendEvent({
      id: `agent-execute-complete-${executionId}`,
      type: "execution_completed",
      module: "agents",
      organizationId: payload.org,
      userId: payload.sub,
      timestamp: new Date().toISOString(),
      data: {
        executionId,
        agentId: body.agentId,
        output: aiResponse.content,
        usage: aiResponse.usage,
        cost: totalCost,
        latency: totalLatency,
        success: true,
        sessionId,
        toolCallsCount: toolCalls.length,
        knowledgeResultsCount: knowledgeResults.length,
        conversationSessionId: activeSessionId,
      },
      version: 1,
    });

    return NextResponse.json({
      success: true,
      data: {
        executionId,
        output: aiResponse.content,
        usage: aiResponse.usage,
        cost: totalCost,
        latency: totalLatency,
        provider: aiResponse.provider,
        model: aiResponse.model,
        sessionId,
        conversationSessionId: activeSessionId,
        toolCalls,
        knowledgeResults: knowledgeResults.slice(0, 5), // Return top 5 knowledge results
        citations: knowledgeResults.map((kr) => kr.source),
      },
    });
  } catch (error) {
    console.error("Error executing agent:", error);

    // Update execution record with error if it was created
    if (executionId && payload) {
      try {
        await db.updateExecution(executionId, payload.org, {
          status: "failed",
          error: error instanceof Error ? error.message : "Unknown error",
          duration: Date.now() - startTime,
        });
      } catch (updateError) {
        console.error("Failed to update execution with error:", updateError);
      }
    }

    // Update session with error if it was created
    if (sessionId && payload) {
      try {
        const sessionManager = getSessionManager(payload.org);
        await sessionManager.updateSession(sessionId, {
          error: error instanceof Error ? error.message : "Unknown error",
          endTime: new Date().toISOString(),
          success: false,
        });
      } catch (sessionError) {
        console.error("Failed to update session with error:", sessionError);
      }
    }

    // Send execution error event
    if (payload) {
      try {
        const apixClient = getAPXClient(
          payload.org,
          payload.sub,
          request.headers.get("authorization")!.substring(7),
        );
        await apixClient.sendEvent({
          id: `agent-execute-error-${executionId || Date.now()}`,
          type: "execution_error",
          module: "agents",
          organizationId: payload.org,
          userId: payload.sub,
          timestamp: new Date().toISOString(),
          data: {
            executionId,
            error: error instanceof Error ? error.message : "Unknown error",
            sessionId,
          },
          version: 1,
        });
      } catch (apixError) {
        console.warn("Failed to send APIX error event:", apixError);
      }
    }

    return NextResponse.json(
      {
        success: false,
        error: "Internal server error",
        message: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 },
    );
  }
}

// Helper function to search knowledge sources
async function searchKnowledge(
  query: string,
  sources: Array<{ id: string; name: string; type: string }>,
  organizationId: string,
): Promise<KnowledgeResult[]> {
  // In production, this would integrate with a vector database like Pinecone or Weaviate
  // For now, return mock knowledge results
  const mockResults: KnowledgeResult[] = [
    {
      id: "kb-1",
      content: `Relevant information about ${query} from company knowledge base. This content would be retrieved from vector search.`,
      source: sources[0]?.name || "Knowledge Base",
      relevanceScore: 0.95,
      metadata: {
        sourceType: sources[0]?.type || "document",
        lastUpdated: new Date().toISOString(),
      },
    },
    {
      id: "kb-2",
      content: `Additional context related to ${query} from documentation. This would include specific procedures and guidelines.`,
      source: "Documentation",
      relevanceScore: 0.87,
      metadata: {
        sourceType: "documentation",
        lastUpdated: new Date().toISOString(),
      },
    },
  ];

  return mockResults;
}

// Helper function to execute tool calls
async function executeToolCall(
  toolCall: any,
  availableTools: Array<{
    id: string;
    name: string;
    description: string;
    parameters: Record<string, any>;
  }>,
  organizationId: string,
  userId: string,
  executionId: string,
): Promise<{ result: any; cost?: number }> {
  // Find the tool definition
  const tool = availableTools.find((t) => t.name === toolCall.function.name);
  if (!tool) {
    throw new Error(`Tool ${toolCall.function.name} not found`);
  }

  // In production, this would:
  // 1. Validate tool parameters against schema
  // 2. Execute the actual tool (API call, database query, etc.)
  // 3. Handle authentication and rate limiting
  // 4. Track usage and costs

  // Mock tool execution
  const mockResult = {
    toolName: toolCall.function.name,
    parameters: toolCall.function.arguments,
    result: `Mock result from ${toolCall.function.name} with parameters: ${JSON.stringify(toolCall.function.arguments)}`,
    timestamp: new Date().toISOString(),
    executionId,
  };

  return {
    result: mockResult,
    cost: 0.001, // Mock cost
  };
}
