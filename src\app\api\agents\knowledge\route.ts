// Production API Route for Agent-Knowledge Integration
// Handles knowledge base connections and RAG configuration for agents

import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/database";
import { verifyToken, hasPermission } from "@/lib/auth-context";
import { getAPXClient } from "@/lib/apix-client";
import { getSessionManager } from "@/lib/session-manager";

interface KnowledgeSource {
  id: string;
  name: string;
  type: "documents" | "database" | "api" | "vector_store";
  enabled: boolean;
  configuration: {
    searchThreshold?: number;
    maxResults?: number;
    indexName?: string;
    filters?: Record<string, any>;
  };
}

interface AgentKnowledgeRequest {
  agentId: string;
  knowledgeSources: KnowledgeSource[];
  ragConfiguration: {
    enabled: boolean;
    similarityThreshold: number;
    maxResults: number;
    reranking: boolean;
    contextWindow: number;
  };
}

// GET /api/agents/knowledge - Get available knowledge sources for agent integration
export async function GET(request: NextRequest) {
  try {
    const authHeader = request.headers.get("authorization");
    if (!authHeader?.startsWith("Bearer ")) {
      return NextResponse.json(
        { error: "Missing or invalid authorization header" },
        { status: 401 },
      );
    }

    const token = authHeader.substring(7);
    const payload = await verifyToken(token);
    if (!payload) {
      return NextResponse.json(
        { error: "Invalid or expired token" },
        { status: 401 },
      );
    }

    if (
      !hasPermission(payload.role, payload.permissions, "knowledge", "read")
    ) {
      return NextResponse.json(
        { error: "Insufficient permissions to read knowledge sources" },
        { status: 403 },
      );
    }

    const { searchParams } = new URL(request.url);
    const agentId = searchParams.get("agentId");

    // Mock knowledge sources - in production, this would query actual knowledge base
    const knowledgeSources = [
      {
        id: "docs-1",
        name: "Company Documentation",
        type: "documents" as const,
        description: "Internal company docs and policies",
        itemCount: 1247,
        lastUpdated: new Date().toISOString(),
        status: "active",
        vectorized: true,
      },
      {
        id: "faq-1",
        name: "FAQ Database",
        type: "database" as const,
        description: "Frequently asked questions and answers",
        itemCount: 456,
        lastUpdated: new Date().toISOString(),
        status: "active",
        vectorized: true,
      },
      {
        id: "support-1",
        name: "Support Tickets",
        type: "database" as const,
        description: "Historical support conversations",
        itemCount: 3421,
        lastUpdated: new Date().toISOString(),
        status: "active",
        vectorized: true,
      },
      {
        id: "manual-1",
        name: "Product Manual",
        type: "documents" as const,
        description: "Technical product documentation",
        itemCount: 89,
        lastUpdated: new Date().toISOString(),
        status: "active",
        vectorized: true,
      },
    ];

    // Get current knowledge configuration for agent if specified
    let currentConfig = null;
    if (agentId) {
      const agent = await db.getAgent(agentId, payload.org);
      if (agent && agent.metadata?.knowledgeConfiguration) {
        currentConfig = agent.metadata.knowledgeConfiguration;
      }
    }

    // Enhance sources with current configuration
    const enhancedSources = knowledgeSources.map((source) => ({
      ...source,
      isEnabled:
        currentConfig?.knowledgeSources?.some(
          (s: any) => s.id === source.id && s.enabled,
        ) || false,
      configuration:
        currentConfig?.knowledgeSources?.find((s: any) => s.id === source.id)
          ?.configuration || {},
    }));

    return NextResponse.json({
      success: true,
      data: {
        knowledgeSources: enhancedSources,
        ragConfiguration: currentConfig?.ragConfiguration || {
          enabled: false,
          similarityThreshold: 0.7,
          maxResults: 5,
          reranking: true,
          contextWindow: 4000,
        },
        vectorStoreStatus: {
          connected: true,
          totalVectors: 15234,
          lastSync: new Date().toISOString(),
        },
      },
    });
  } catch (error) {
    console.error("Error fetching agent knowledge sources:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 },
    );
  }
}

// POST /api/agents/knowledge - Configure knowledge integration for an agent
export async function POST(request: NextRequest) {
  try {
    const authHeader = request.headers.get("authorization");
    if (!authHeader?.startsWith("Bearer ")) {
      return NextResponse.json(
        { error: "Missing or invalid authorization header" },
        { status: 401 },
      );
    }

    const token = authHeader.substring(7);
    const payload = await verifyToken(token);
    if (!payload) {
      return NextResponse.json(
        { error: "Invalid or expired token" },
        { status: 401 },
      );
    }

    if (!hasPermission(payload.role, payload.permissions, "agents", "update")) {
      return NextResponse.json(
        { error: "Insufficient permissions to update agents" },
        { status: 403 },
      );
    }

    const body: AgentKnowledgeRequest = await request.json();

    if (!body.agentId) {
      return NextResponse.json(
        { error: "Agent ID is required" },
        { status: 400 },
      );
    }

    // Verify agent exists and user has access
    const agent = await db.getAgent(body.agentId, payload.org);
    if (!agent) {
      return NextResponse.json({ error: "Agent not found" }, { status: 404 });
    }

    // Validate RAG configuration
    if (
      body.ragConfiguration.similarityThreshold < 0.1 ||
      body.ragConfiguration.similarityThreshold > 1.0
    ) {
      return NextResponse.json(
        { error: "Similarity threshold must be between 0.1 and 1.0" },
        { status: 400 },
      );
    }

    if (
      body.ragConfiguration.maxResults < 1 ||
      body.ragConfiguration.maxResults > 50
    ) {
      return NextResponse.json(
        { error: "Max results must be between 1 and 50" },
        { status: 400 },
      );
    }

    // Update agent with knowledge configuration
    const knowledgeConfiguration = {
      knowledgeSources: body.knowledgeSources,
      ragConfiguration: body.ragConfiguration,
      lastUpdated: new Date().toISOString(),
      enabledSources: body.knowledgeSources.filter((s) => s.enabled).length,
    };

    const updatedAgent = await db.updateAgent(body.agentId, payload.org, {
      metadata: {
        ...agent.metadata,
        knowledgeConfiguration,
        knowledgeIntegrationEnabled: body.ragConfiguration.enabled,
        lastKnowledgeUpdate: new Date().toISOString(),
      },
    });

    // Create session for this integration
    const sessionManager = getSessionManager(payload.org);
    const sessionId = await sessionManager.createSession(
      "agents",
      "system",
      {
        action: "knowledge_integration",
        agentId: body.agentId,
        knowledgeConfiguration,
        enabledSources: body.knowledgeSources.filter((s) => s.enabled).length,
      },
      {
        tags: ["api", "agents", "knowledge", "rag"],
        conversationId: `knowledge-${Date.now()}`,
      },
    );

    // Send real-time event
    try {
      const apixClient = getAPXClient(payload.org, payload.sub, token);
      await apixClient.sendEvent({
        id: `agent-knowledge-updated-${body.agentId}`,
        type: "knowledge_integrated",
        module: "agents",
        organizationId: payload.org,
        userId: payload.sub,
        timestamp: new Date().toISOString(),
        data: {
          agentId: body.agentId,
          knowledgeConfiguration,
          ragEnabled: body.ragConfiguration.enabled,
          enabledSources: body.knowledgeSources.filter((s) => s.enabled).length,
          sessionId,
        },
        version: 1,
      });
    } catch (apixError) {
      console.warn("Failed to send APIX event:", apixError);
    }

    return NextResponse.json({
      success: true,
      data: {
        agent: updatedAgent,
        knowledgeConfiguration,
        sessionId,
        integrationStatus: {
          enabled: body.ragConfiguration.enabled,
          sourceCount: body.knowledgeSources.length,
          enabledCount: body.knowledgeSources.filter((s) => s.enabled).length,
          ragConfigured: true,
        },
      },
    });
  } catch (error) {
    console.error("Error configuring agent knowledge:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 },
    );
  }
}
