// Production API Route for Agent Rollback
// Safe rollback with validation and backup

import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/database";
import { verifyToken, hasPermission } from "@/lib/auth-context";
import { getAPXClient } from "@/lib/apix-client";
import { getSessionManager } from "@/lib/session-manager";

interface RollbackRequest {
  agentId: string;
  targetVersion: string;
  reason?: string;
  createBackup?: boolean;
}

// POST /api/agents/rollback - Rollback agent to previous version
export async function POST(request: NextRequest) {
  try {
    const authHeader = request.headers.get("authorization");
    if (!authHeader?.startsWith("Bearer ")) {
      return NextResponse.json(
        { error: "Missing or invalid authorization header" },
        { status: 401 },
      );
    }

    const token = authHeader.substring(7);
    const payload = await verifyToken(token);
    if (!payload) {
      return NextResponse.json(
        { error: "Invalid or expired token" },
        { status: 401 },
      );
    }

    if (!hasPermission(payload.role, payload.permissions, "agents", "update")) {
      return NextResponse.json(
        { error: "Insufficient permissions to rollback agents" },
        { status: 403 },
      );
    }

    const body: RollbackRequest = await request.json();

    if (!body.agentId || !body.targetVersion) {
      return NextResponse.json(
        { error: "Agent ID and target version are required" },
        { status: 400 },
      );
    }

    // Verify agent exists and user has access
    const agent = await db.getAgent(body.agentId, payload.org);
    if (!agent) {
      return NextResponse.json({ error: "Agent not found" }, { status: 404 });
    }

    // Get target version details
    const targetVersion = await getAgentVersionDetails(
      body.agentId,
      body.targetVersion,
      payload.org,
    );
    if (!targetVersion) {
      return NextResponse.json(
        { error: "Target version not found" },
        { status: 404 },
      );
    }

    // Create backup of current version if requested
    let backupVersion = null;
    if (body.createBackup !== false) {
      backupVersion = await createBackupVersion(
        agent,
        payload.sub,
        payload.org,
      );
    }

    // Perform rollback
    const rollbackData = {
      systemPrompt: targetVersion.changes.systemPrompt || agent.systemPrompt,
      temperature: targetVersion.changes.temperature || agent.temperature,
      maxTokens: targetVersion.changes.maxTokens || agent.maxTokens,
      model: targetVersion.changes.model || agent.model,
      provider: targetVersion.changes.provider || agent.provider,
      version: body.targetVersion,
      metadata: {
        ...agent.metadata,
        ...targetVersion.changes.metadata,
        rollbackInfo: {
          previousVersion: agent.version,
          rollbackReason: body.reason,
          rollbackAt: new Date().toISOString(),
          rollbackBy: payload.sub,
          backupVersionId: backupVersion?.id,
        },
      },
    };

    const updatedAgent = await db.updateAgent(
      body.agentId,
      payload.org,
      rollbackData,
    );

    // Create session for rollback
    const sessionManager = getSessionManager(payload.org);
    const sessionId = await sessionManager.createSession(
      "agents",
      "user",
      {
        action: "rollback",
        agentId: body.agentId,
        fromVersion: agent.version,
        toVersion: body.targetVersion,
        reason: body.reason,
        backupCreated: !!backupVersion,
      },
      {
        tags: ["api", "agents", "rollback", "version-control"],
        conversationId: `api-rollback-${Date.now()}`,
      },
    );

    // Send real-time event
    try {
      const apixClient = getAPXClient(payload.org, payload.sub, token);
      await apixClient.sendEvent({
        id: `agent-rollback-${body.agentId}`,
        type: "agent_rollback",
        module: "agents",
        organizationId: payload.org,
        userId: payload.sub,
        timestamp: new Date().toISOString(),
        data: {
          agentId: body.agentId,
          fromVersion: agent.version,
          toVersion: body.targetVersion,
          reason: body.reason,
          backupVersionId: backupVersion?.id,
          sessionId,
        },
        version: 1,
      });
    } catch (apixError) {
      console.warn("Failed to send APIX event:", apixError);
    }

    return NextResponse.json({
      success: true,
      data: {
        agent: updatedAgent,
        rollbackInfo: {
          fromVersion: agent.version,
          toVersion: body.targetVersion,
          backupVersion: backupVersion,
        },
        sessionId,
      },
    });
  } catch (error) {
    console.error("Error rolling back agent:", error);
    return NextResponse.json(
      {
        error: "Internal server error",
        message: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 },
    );
  }
}

// Helper functions
async function getAgentVersionDetails(
  agentId: string,
  version: string,
  organizationId: string,
) {
  // Mock implementation - in production, query version database
  return {
    id: `version-${version}`,
    version,
    changes: {
      systemPrompt: "Previous system prompt content",
      temperature: 0.7,
      maxTokens: 2048,
    },
    createdAt: new Date().toISOString(),
  };
}

async function createBackupVersion(
  agent: any,
  userId: string,
  organizationId: string,
) {
  // Mock implementation - in production, create backup in database
  return {
    id: `backup-${Date.now()}`,
    version: `${agent.version}-backup`,
    changes: {
      systemPrompt: agent.systemPrompt,
      temperature: agent.temperature,
      maxTokens: agent.maxTokens,
      model: agent.model,
      provider: agent.provider,
    },
    createdAt: new Date().toISOString(),
    createdBy: userId,
    isBackup: true,
  };
}
