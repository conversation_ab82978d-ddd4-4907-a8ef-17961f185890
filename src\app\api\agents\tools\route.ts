// Production API Route for Agent-Tool Integration
// Handles tool discovery, binding, and execution for agents

import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/database";
import { verifyToken, hasPermission } from "@/lib/auth-context";
import { getAPXClient } from "@/lib/apix-client";
import { getSessionManager } from "@/lib/session-manager";

interface ToolBinding {
  toolId: string;
  enabled: boolean;
  configuration?: Record<string, any>;
  permissions?: string[];
}

interface AgentToolsRequest {
  agentId: string;
  tools: ToolBinding[];
}

// GET /api/agents/tools - Get available tools for agent integration
export async function GET(request: NextRequest) {
  try {
    const authHeader = request.headers.get("authorization");
    if (!authHeader?.startsWith("Bearer ")) {
      return NextResponse.json(
        { error: "Missing or invalid authorization header" },
        { status: 401 },
      );
    }

    const token = authHeader.substring(7);
    const payload = await verifyToken(token);
    if (!payload) {
      return NextResponse.json(
        { error: "Invalid or expired token" },
        { status: 401 },
      );
    }

    if (!hasPermission(payload.role, payload.permissions, "tools", "read")) {
      return NextResponse.json(
        { error: "Insufficient permissions to read tools" },
        { status: 403 },
      );
    }

    const { searchParams } = new URL(request.url);
    const agentId = searchParams.get("agentId");
    const category = searchParams.get("category");

    // Get available tools
    const { tools } = await db.listTools(payload.org, {
      status: "active",
      ...(category && { category }),
    });

    // Get current tool bindings for agent if specified
    let currentBindings: ToolBinding[] = [];
    if (agentId) {
      const agent = await db.getAgent(agentId, payload.org);
      if (agent && agent.metadata?.toolBindings) {
        currentBindings = agent.metadata.toolBindings;
      }
    }

    // Enhance tools with binding status
    const enhancedTools = tools.map((tool) => ({
      ...tool,
      isEnabled: currentBindings.some((b) => b.toolId === tool.id && b.enabled),
      configuration: currentBindings.find((b) => b.toolId === tool.id)
        ?.configuration,
      integrationReady: true,
      capabilities: {
        async: tool.timeout > 5000,
        streaming: tool.metadata?.supportsStreaming || false,
        batching: tool.metadata?.supportsBatching || false,
        caching: tool.caching.enabled,
      },
    }));

    return NextResponse.json({
      success: true,
      data: {
        tools: enhancedTools,
        categories: [...new Set(tools.map((t) => t.category))],
        totalCount: tools.length,
        enabledCount: currentBindings.filter((b) => b.enabled).length,
      },
    });
  } catch (error) {
    console.error("Error fetching agent tools:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 },
    );
  }
}

// POST /api/agents/tools - Configure tool bindings for an agent
export async function POST(request: NextRequest) {
  try {
    const authHeader = request.headers.get("authorization");
    if (!authHeader?.startsWith("Bearer ")) {
      return NextResponse.json(
        { error: "Missing or invalid authorization header" },
        { status: 401 },
      );
    }

    const token = authHeader.substring(7);
    const payload = await verifyToken(token);
    if (!payload) {
      return NextResponse.json(
        { error: "Invalid or expired token" },
        { status: 401 },
      );
    }

    if (!hasPermission(payload.role, payload.permissions, "agents", "update")) {
      return NextResponse.json(
        { error: "Insufficient permissions to update agents" },
        { status: 403 },
      );
    }

    const body: AgentToolsRequest = await request.json();

    if (!body.agentId) {
      return NextResponse.json(
        { error: "Agent ID is required" },
        { status: 400 },
      );
    }

    // Verify agent exists and user has access
    const agent = await db.getAgent(body.agentId, payload.org);
    if (!agent) {
      return NextResponse.json({ error: "Agent not found" }, { status: 404 });
    }

    // Validate tool bindings
    const validatedBindings: ToolBinding[] = [];
    for (const binding of body.tools) {
      const tool = await db.getTool(binding.toolId, payload.org);
      if (!tool) {
        return NextResponse.json(
          { error: `Tool ${binding.toolId} not found` },
          { status: 400 },
        );
      }

      if (tool.status !== "active") {
        return NextResponse.json(
          { error: `Tool ${tool.name} is not active` },
          { status: 400 },
        );
      }

      validatedBindings.push({
        toolId: binding.toolId,
        enabled: binding.enabled,
        configuration: binding.configuration || {},
        permissions: binding.permissions || ["execute"],
      });
    }

    // Update agent with tool bindings
    const updatedAgent = await db.updateAgent(body.agentId, payload.org, {
      metadata: {
        ...agent.metadata,
        toolBindings: validatedBindings,
        toolIntegrationEnabled: validatedBindings.some((b) => b.enabled),
        lastToolUpdate: new Date().toISOString(),
      },
    });

    // Create session for this integration
    const sessionManager = getSessionManager(payload.org);
    const sessionId = await sessionManager.createSession(
      "agents",
      "system",
      {
        action: "tool_integration",
        agentId: body.agentId,
        toolBindings: validatedBindings,
        enabledTools: validatedBindings.filter((b) => b.enabled).length,
      },
      {
        tags: ["api", "agents", "tools", "integration"],
        conversationId: `integration-${Date.now()}`,
      },
    );

    // Send real-time event
    try {
      const apixClient = getAPXClient(payload.org, payload.sub, token);
      await apixClient.sendEvent({
        id: `agent-tools-updated-${body.agentId}`,
        type: "tools_integrated",
        module: "agents",
        organizationId: payload.org,
        userId: payload.sub,
        timestamp: new Date().toISOString(),
        data: {
          agentId: body.agentId,
          toolBindings: validatedBindings,
          enabledCount: validatedBindings.filter((b) => b.enabled).length,
          sessionId,
        },
        version: 1,
      });
    } catch (apixError) {
      console.warn("Failed to send APIX event:", apixError);
    }

    return NextResponse.json({
      success: true,
      data: {
        agent: updatedAgent,
        toolBindings: validatedBindings,
        sessionId,
        integrationStatus: {
          enabled: validatedBindings.some((b) => b.enabled),
          toolCount: validatedBindings.length,
          enabledCount: validatedBindings.filter((b) => b.enabled).length,
        },
      },
    });
  } catch (error) {
    console.error("Error configuring agent tools:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 },
    );
  }
}
