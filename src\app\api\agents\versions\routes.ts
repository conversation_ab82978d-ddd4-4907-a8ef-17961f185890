// Production API Route for Agent Version Management
// Comprehensive versioning with rollback capabilities and A/B testing

import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/database";
import { verifyToken, hasPermission } from "@/lib/auth-context";
import { getAPXClient } from "@/lib/apix-client";
import { getSessionManager } from "@/lib/session-manager";

interface CreateVersionRequest {
  agentId: string;
  version: string;
  description?: string;
  changes: {
    systemPrompt?: string;
    temperature?: number;
    maxTokens?: number;
    model?: string;
    provider?: string;
    metadata?: Record<string, any>;
  };
  isMinor?: boolean;
  tags?: string[];
}

interface AgentVersion {
  id: string;
  agentId: string;
  version: string;
  description: string;
  changes: Record<string, any>;
  createdAt: string;
  createdBy: string;
  isActive: boolean;
  performance?: {
    successRate: number;
    avgLatency: number;
    totalExecutions: number;
    avgCost: number;
    userSatisfaction: number;
  };
  abTestResults?: {
    testId: string;
    trafficPercentage: number;
    conversionRate: number;
    statisticalSignificance: number;
  };
}

// GET /api/agents/versions - List agent versions
export async function GET(request: NextRequest) {
  try {
    const authHeader = request.headers.get("authorization");
    if (!authHeader?.startsWith("Bearer ")) {
      return NextResponse.json(
        { error: "Missing or invalid authorization header" },
        { status: 401 },
      );
    }

    const token = authHeader.substring(7);
    const payload = await verifyToken(token);
    if (!payload) {
      return NextResponse.json(
        { error: "Invalid or expired token" },
        { status: 401 },
      );
    }

    if (!hasPermission(payload.role, payload.permissions, "agents", "read")) {
      return NextResponse.json(
        { error: "Insufficient permissions to read agent versions" },
        { status: 403 },
      );
    }

    const { searchParams } = new URL(request.url);
    const agentId = searchParams.get("agentId");
    const page = parseInt(searchParams.get("page") || "1");
    const limit = Math.min(parseInt(searchParams.get("limit") || "20"), 100);
    const offset = (page - 1) * limit;

    if (!agentId) {
      return NextResponse.json(
        { error: "Agent ID is required" },
        { status: 400 },
      );
    }

    // Verify agent exists and user has access
    const agent = await db.getAgent(agentId, payload.org);
    if (!agent) {
      return NextResponse.json({ error: "Agent not found" }, { status: 404 });
    }

    // Get agent versions (mock implementation)
    const versions = await getAgentVersions(
      agentId,
      payload.org,
      limit,
      offset,
    );
    const total = await getAgentVersionsCount(agentId, payload.org);

    // Create session for this API call
    const sessionManager = getSessionManager(payload.org);
    const sessionId = await sessionManager.createSession(
      "agents",
      "user",
      { action: "list_versions", agentId, versions: versions.length },
      {
        tags: ["api", "agents", "versions", "list"],
        conversationId: `api-versions-${Date.now()}`,
      },
    );

    // Send real-time event
    try {
      const apixClient = getAPXClient(payload.org, payload.sub, token);
      await apixClient.sendEvent({
        id: `agent-versions-listed-${Date.now()}`,
        type: "versions_listed",
        module: "agents",
        organizationId: payload.org,
        userId: payload.sub,
        timestamp: new Date().toISOString(),
        data: {
          agentId,
          versionsCount: versions.length,
          sessionId,
        },
        version: 1,
      });
    } catch (apixError) {
      console.warn("Failed to send APIX event:", apixError);
    }

    return NextResponse.json({
      success: true,
      data: {
        versions,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
        },
        sessionId,
      },
    });
  } catch (error) {
    console.error("Error listing agent versions:", error);
    return NextResponse.json(
      {
        error: "Internal server error",
        message: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 },
    );
  }
}

// POST /api/agents/versions - Create new agent version
export async function POST(request: NextRequest) {
  try {
    const authHeader = request.headers.get("authorization");
    if (!authHeader?.startsWith("Bearer ")) {
      return NextResponse.json(
        { error: "Missing or invalid authorization header" },
        { status: 401 },
      );
    }

    const token = authHeader.substring(7);
    const payload = await verifyToken(token);
    if (!payload) {
      return NextResponse.json(
        { error: "Invalid or expired token" },
        { status: 401 },
      );
    }

    if (!hasPermission(payload.role, payload.permissions, "agents", "update")) {
      return NextResponse.json(
        { error: "Insufficient permissions to create agent versions" },
        { status: 403 },
      );
    }

    const body: CreateVersionRequest = await request.json();

    if (!body.agentId || !body.version) {
      return NextResponse.json(
        { error: "Agent ID and version are required" },
        { status: 400 },
      );
    }

    // Verify agent exists and user has access
    const agent = await db.getAgent(body.agentId, payload.org);
    if (!agent) {
      return NextResponse.json({ error: "Agent not found" }, { status: 404 });
    }

    // Check if version already exists
    const existingVersion = await getAgentVersion(
      body.agentId,
      body.version,
      payload.org,
    );
    if (existingVersion) {
      return NextResponse.json(
        { error: "Version already exists" },
        { status: 409 },
      );
    }

    // Create new version
    const newVersion = await createAgentVersion({
      agentId: body.agentId,
      version: body.version,
      description: body.description || "",
      changes: body.changes,
      createdBy: payload.sub,
      organizationId: payload.org,
      isMinor: body.isMinor || false,
      tags: body.tags || [],
    });

    // Create session for this creation
    const sessionManager = getSessionManager(payload.org);
    const sessionId = await sessionManager.createSession(
      "agents",
      "user",
      { action: "create_version", agentId: body.agentId, version: newVersion },
      {
        tags: ["api", "agents", "versions", "create"],
        conversationId: `api-create-version-${Date.now()}`,
      },
    );

    // Send real-time event
    try {
      const apixClient = getAPXClient(payload.org, payload.sub, token);
      await apixClient.sendEvent({
        id: `agent-version-created-${newVersion.id}`,
        type: "version_created",
        module: "agents",
        organizationId: payload.org,
        userId: payload.sub,
        timestamp: new Date().toISOString(),
        data: {
          agentId: body.agentId,
          version: newVersion,
          sessionId,
        },
        version: 1,
      });
    } catch (apixError) {
      console.warn("Failed to send APIX event:", apixError);
    }

    return NextResponse.json(
      {
        success: true,
        data: {
          version: newVersion,
          sessionId,
        },
      },
      { status: 201 },
    );
  } catch (error) {
    console.error("Error creating agent version:", error);
    return NextResponse.json(
      {
        error: "Internal server error",
        message: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 },
    );
  }
}

// Helper functions for version management
async function getAgentVersions(
  agentId: string,
  organizationId: string,
  limit: number,
  offset: number,
): Promise<AgentVersion[]> {
  // Mock implementation - in production, this would query the database
  const mockVersions: AgentVersion[] = [
    {
      id: "version-1",
      agentId,
      version: "2.1.0",
      description: "Improved response accuracy and reduced latency",
      changes: {
        systemPrompt: "Enhanced with better context understanding",
        temperature: 0.7,
        maxTokens: 2048,
      },
      createdAt: new Date().toISOString(),
      createdBy: "user-123",
      isActive: true,
      performance: {
        successRate: 0.95,
        avgLatency: 1200,
        totalExecutions: 1500,
        avgCost: 0.025,
        userSatisfaction: 4.7,
      },
      abTestResults: {
        testId: "ab-test-1",
        trafficPercentage: 50,
        conversionRate: 0.12,
        statisticalSignificance: 0.95,
      },
    },
    {
      id: "version-2",
      agentId,
      version: "2.0.0",
      description: "Major update with tool integration",
      changes: {
        systemPrompt: "Added tool calling capabilities",
        metadata: { toolsEnabled: true },
      },
      createdAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
      createdBy: "user-123",
      isActive: false,
      performance: {
        successRate: 0.92,
        avgLatency: 1400,
        totalExecutions: 2300,
        avgCost: 0.032,
        userSatisfaction: 4.5,
      },
    },
  ];

  return mockVersions.slice(offset, offset + limit);
}

async function getAgentVersionsCount(
  agentId: string,
  organizationId: string,
): Promise<number> {
  // Mock implementation
  return 15;
}

async function getAgentVersion(
  agentId: string,
  version: string,
  organizationId: string,
): Promise<AgentVersion | null> {
  // Mock implementation
  return null;
}

async function createAgentVersion(data: {
  agentId: string;
  version: string;
  description: string;
  changes: Record<string, any>;
  createdBy: string;
  organizationId: string;
  isMinor: boolean;
  tags: string[];
}): Promise<AgentVersion> {
  // Mock implementation - in production, this would create in database
  const newVersion: AgentVersion = {
    id: `version-${Date.now()}`,
    agentId: data.agentId,
    version: data.version,
    description: data.description,
    changes: data.changes,
    createdAt: new Date().toISOString(),
    createdBy: data.createdBy,
    isActive: false,
  };

  return newVersion;
}
