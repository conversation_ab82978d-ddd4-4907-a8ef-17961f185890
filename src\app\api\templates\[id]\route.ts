// Production API Routes for Individual Template Management
// CRUD operations with versioning and inheritance

import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/database";
import { verifyToken, hasPermission } from "@/lib/auth-context";
import { getAPXClient } from "@/lib/apix-client";
import { getSessionManager } from "@/lib/session-manager";

interface UpdateTemplateRequest {
  name?: string;
  description?: string;
  content?: string;
  category?: string;
  tags?: string[];
  variables?: Array<{
    name: string;
    type: string;
    description: string;
    required: boolean;
    defaultValue?: any;
    validation?: {
      min?: number;
      max?: number;
      pattern?: string;
      enum?: string[];
    };
  }>;
  isPublic?: boolean;
  version?: string;
  parentTemplateId?: string;
  metadata?: Record<string, any>;
}

// GET /api/templates/[id] - Get specific template
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } },
) {
  try {
    const authHeader = request.headers.get("authorization");
    if (!authHeader?.startsWith("Bearer ")) {
      return NextResponse.json(
        { error: "Missing or invalid authorization header" },
        { status: 401 },
      );
    }

    const token = authHeader.substring(7);
    const payload = await verifyToken(token);
    if (!payload) {
      return NextResponse.json(
        { error: "Invalid or expired token" },
        { status: 401 },
      );
    }

    if (
      !hasPermission(payload.role, payload.permissions, "templates", "read")
    ) {
      return NextResponse.json(
        { error: "Insufficient permissions to read templates" },
        { status: 403 },
      );
    }

    const templateId = params.id;
    if (!templateId) {
      return NextResponse.json(
        { error: "Template ID is required" },
        { status: 400 },
      );
    }

    // Fetch template with multi-tenant security
    const template = await db.getTemplate(templateId, payload.org);
    if (!template) {
      return NextResponse.json(
        { error: "Template not found" },
        { status: 404 },
      );
    }

    // Get template inheritance chain if applicable
    const inheritanceChain = await getTemplateInheritanceChain(
      templateId,
      payload.org,
    );

    // Get template versions
    const versions = await getTemplateVersions(templateId, payload.org);

    // Create session for this API call
    const sessionManager = getSessionManager(payload.org);
    const sessionId = await sessionManager.createSession(
      "templates",
      "user",
      { action: "get", templateId, template },
      {
        tags: ["api", "templates", "get"],
        conversationId: `api-get-template-${Date.now()}`,
      },
    );

    // Send real-time event
    try {
      const apixClient = getAPXClient(payload.org, payload.sub, token);
      await apixClient.sendEvent({
        id: `template-viewed-${templateId}`,
        type: "template_viewed",
        module: "templates",
        organizationId: payload.org,
        userId: payload.sub,
        timestamp: new Date().toISOString(),
        data: {
          templateId,
          sessionId,
        },
        version: 1,
      });
    } catch (apixError) {
      console.warn("Failed to send APIX event:", apixError);
    }

    return NextResponse.json({
      success: true,
      data: {
        template,
        inheritanceChain,
        versions,
        sessionId,
      },
    });
  } catch (error) {
    console.error("Error fetching template:", error);
    return NextResponse.json(
      {
        error: "Internal server error",
        message: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 },
    );
  }
}

// PUT /api/templates/[id] - Update template
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } },
) {
  try {
    const authHeader = request.headers.get("authorization");
    if (!authHeader?.startsWith("Bearer ")) {
      return NextResponse.json(
        { error: "Missing or invalid authorization header" },
        { status: 401 },
      );
    }

    const token = authHeader.substring(7);
    const payload = await verifyToken(token);
    if (!payload) {
      return NextResponse.json(
        { error: "Invalid or expired token" },
        { status: 401 },
      );
    }

    if (
      !hasPermission(payload.role, payload.permissions, "templates", "update")
    ) {
      return NextResponse.json(
        { error: "Insufficient permissions to update templates" },
        { status: 403 },
      );
    }

    const templateId = params.id;
    if (!templateId) {
      return NextResponse.json(
        { error: "Template ID is required" },
        { status: 400 },
      );
    }

    // Check if template exists and user has access
    const existingTemplate = await db.getTemplate(templateId, payload.org);
    if (!existingTemplate) {
      return NextResponse.json(
        { error: "Template not found" },
        { status: 404 },
      );
    }

    const body: UpdateTemplateRequest = await request.json();

    // Validation
    if (body.name !== undefined && !body.name.trim()) {
      return NextResponse.json(
        { error: "Template name cannot be empty" },
        { status: 400 },
      );
    }

    if (body.content !== undefined && !body.content.trim()) {
      return NextResponse.json(
        { error: "Template content cannot be empty" },
        { status: 400 },
      );
    }

    // Validate template variables
    if (body.variables) {
      const validationError = validateTemplateVariables(body.variables);
      if (validationError) {
        return NextResponse.json({ error: validationError }, { status: 400 });
      }
    }

    // Create new version if content changed significantly
    let newVersion = existingTemplate.version;
    if (body.content && body.content !== existingTemplate.content) {
      newVersion = incrementVersion(existingTemplate.version, body.version);

      // Create version history entry
      await createTemplateVersion({
        templateId,
        version: newVersion,
        changes: body,
        previousVersion: existingTemplate.version,
        createdBy: payload.sub,
        organizationId: payload.org,
      });
    }

    // Prepare update data
    const updateData: any = {
      ...(body.name !== undefined && { name: body.name.trim() }),
      ...(body.description !== undefined && {
        description: body.description.trim(),
      }),
      ...(body.content !== undefined && { content: body.content.trim() }),
      ...(body.category !== undefined && { category: body.category }),
      ...(body.tags !== undefined && { tags: body.tags }),
      ...(body.variables !== undefined && { variables: body.variables }),
      ...(body.isPublic !== undefined && { isPublic: body.isPublic }),
      version: newVersion,
      ...(body.metadata !== undefined && {
        metadata: { ...existingTemplate.metadata, ...body.metadata },
      }),
    };

    // Handle template inheritance
    if (body.parentTemplateId) {
      const parentTemplate = await db.getTemplate(
        body.parentTemplateId,
        payload.org,
      );
      if (!parentTemplate) {
        return NextResponse.json(
          { error: "Parent template not found" },
          { status: 400 },
        );
      }

      // Merge parent template content and variables
      updateData.content = mergeTemplateContent(
        parentTemplate.content,
        updateData.content || existingTemplate.content,
      );
      updateData.variables = mergeTemplateVariables(
        parentTemplate.variables,
        updateData.variables || existingTemplate.variables,
      );
      updateData.metadata = {
        ...updateData.metadata,
        parentTemplateId: body.parentTemplateId,
        inheritanceLevel: (parentTemplate.metadata?.inheritanceLevel || 0) + 1,
      };
    }

    // Update template (mock implementation)
    const updatedTemplate = await updateTemplate(
      templateId,
      payload.org,
      updateData,
    );

    // Create session for this update
    const sessionManager = getSessionManager(payload.org);
    const sessionId = await sessionManager.createSession(
      "templates",
      "user",
      { action: "update", templateId, changes: updateData, updatedTemplate },
      {
        tags: ["api", "templates", "update"],
        conversationId: `api-update-template-${Date.now()}`,
      },
    );

    // Send real-time event
    try {
      const apixClient = getAPXClient(payload.org, payload.sub, token);
      await apixClient.sendEvent({
        id: `template-updated-${templateId}`,
        type: "template_updated",
        module: "templates",
        organizationId: payload.org,
        userId: payload.sub,
        timestamp: new Date().toISOString(),
        data: {
          templateId,
          template: updatedTemplate,
          changes: updateData,
          newVersion,
          sessionId,
        },
        version: 1,
      });
    } catch (apixError) {
      console.warn("Failed to send APIX event:", apixError);
    }

    return NextResponse.json({
      success: true,
      data: {
        template: updatedTemplate,
        sessionId,
      },
    });
  } catch (error) {
    console.error("Error updating template:", error);
    return NextResponse.json(
      {
        error: "Internal server error",
        message: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 },
    );
  }
}

// DELETE /api/templates/[id] - Delete template
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } },
) {
  try {
    const authHeader = request.headers.get("authorization");
    if (!authHeader?.startsWith("Bearer ")) {
      return NextResponse.json(
        { error: "Missing or invalid authorization header" },
        { status: 401 },
      );
    }

    const token = authHeader.substring(7);
    const payload = await verifyToken(token);
    if (!payload) {
      return NextResponse.json(
        { error: "Invalid or expired token" },
        { status: 401 },
      );
    }

    if (
      !hasPermission(payload.role, payload.permissions, "templates", "delete")
    ) {
      return NextResponse.json(
        { error: "Insufficient permissions to delete templates" },
        { status: 403 },
      );
    }

    const templateId = params.id;
    if (!templateId) {
      return NextResponse.json(
        { error: "Template ID is required" },
        { status: 400 },
      );
    }

    // Check if template exists and user has access
    const existingTemplate = await db.getTemplate(templateId, payload.org);
    if (!existingTemplate) {
      return NextResponse.json(
        { error: "Template not found" },
        { status: 404 },
      );
    }

    // Check if template is being used by agents
    const dependentAgents = await getAgentsUsingTemplate(
      templateId,
      payload.org,
    );
    if (dependentAgents.length > 0) {
      return NextResponse.json(
        {
          error: "Cannot delete template",
          message: `Template is being used by ${dependentAgents.length} agent(s)`,
          dependentAgents: dependentAgents.map((a) => ({
            id: a.id,
            name: a.name,
          })),
        },
        { status: 409 },
      );
    }

    // Soft delete the template
    await deleteTemplate(templateId, payload.org);

    // Create session for this deletion
    const sessionManager = getSessionManager(payload.org);
    const sessionId = await sessionManager.createSession(
      "templates",
      "user",
      { action: "delete", templateId, deletedTemplate: existingTemplate },
      {
        tags: ["api", "templates", "delete"],
        conversationId: `api-delete-template-${Date.now()}`,
      },
    );

    // Send real-time event
    try {
      const apixClient = getAPXClient(payload.org, payload.sub, token);
      await apixClient.sendEvent({
        id: `template-deleted-${templateId}`,
        type: "template_deleted",
        module: "templates",
        organizationId: payload.org,
        userId: payload.sub,
        timestamp: new Date().toISOString(),
        data: {
          templateId,
          sessionId,
        },
        version: 1,
      });
    } catch (apixError) {
      console.warn("Failed to send APIX event:", apixError);
    }

    return NextResponse.json({
      success: true,
      data: {
        message: "Template deleted successfully",
        templateId,
        sessionId,
      },
    });
  } catch (error) {
    console.error("Error deleting template:", error);
    return NextResponse.json(
      {
        error: "Internal server error",
        message: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 },
    );
  }
}

// Helper functions
async function getTemplateInheritanceChain(
  templateId: string,
  organizationId: string,
) {
  // Mock implementation - in production, traverse inheritance chain
  return [];
}

async function getTemplateVersions(templateId: string, organizationId: string) {
  // Mock implementation - in production, get version history
  return [
    {
      version: "1.2.0",
      createdAt: new Date().toISOString(),
      createdBy: "user-123",
      changes: ["Updated system prompt", "Added new variables"],
    },
    {
      version: "1.1.0",
      createdAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
      createdBy: "user-123",
      changes: ["Fixed variable validation"],
    },
  ];
}

function validateTemplateVariables(variables: any[]): string | null {
  for (const variable of variables) {
    if (!variable.name || typeof variable.name !== "string") {
      return "Variable name is required and must be a string";
    }
    if (
      !variable.type ||
      !["string", "number", "boolean", "array", "object"].includes(
        variable.type,
      )
    ) {
      return "Variable type must be one of: string, number, boolean, array, object";
    }
    if (typeof variable.required !== "boolean") {
      return "Variable required field must be a boolean";
    }
  }
  return null;
}

function incrementVersion(
  currentVersion: string,
  requestedVersion?: string,
): string {
  if (requestedVersion) return requestedVersion;

  const parts = currentVersion.split(".").map(Number);
  parts[2] = (parts[2] || 0) + 1; // Increment patch version
  return parts.join(".");
}

async function createTemplateVersion(data: any) {
  // Mock implementation - in production, create version record
  console.log("Creating template version:", data);
}

function mergeTemplateContent(
  parentContent: string,
  childContent: string,
): string {
  // Simple merge - in production, implement sophisticated template inheritance
  return `${parentContent}\n\n${childContent}`;
}

function mergeTemplateVariables(parentVars: any[], childVars: any[]): any[] {
  // Merge variables, child overrides parent
  const merged = [...parentVars];
  childVars.forEach((childVar) => {
    const existingIndex = merged.findIndex((v) => v.name === childVar.name);
    if (existingIndex >= 0) {
      merged[existingIndex] = childVar;
    } else {
      merged.push(childVar);
    }
  });
  return merged;
}

async function updateTemplate(
  templateId: string,
  organizationId: string,
  updateData: any,
) {
  // Mock implementation - in production, update in database
  return {
    id: templateId,
    ...updateData,
    updatedAt: new Date().toISOString(),
  };
}

async function deleteTemplate(templateId: string, organizationId: string) {
  // Mock implementation - in production, soft delete in database
  console.log(`Deleting template ${templateId} for org ${organizationId}`);
}

async function getAgentsUsingTemplate(
  templateId: string,
  organizationId: string,
) {
  // Mock implementation - in production, query agents table
  return [];
}
