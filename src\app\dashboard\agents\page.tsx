"use client";
import React, { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Plus, Filter, Search, Workflow } from "lucide-react";
import { Input } from "@/components/ui/input";
import AgentList from "@/components/agents/AgentList";
import AgentBuilder from "@/components/agents/AgentBuilder";
import AgentCanvasBuilder from "@/components/agents/AgentCanvasBuilder";
import ComplexAgentWorkflow from "@/components/agents/ComplexAgentWorkflow";
import { APXClient, APXEvent, getAPXClient } from "@/lib/apix-client";
import { SessionManager } from "@/lib/session-manager";

export default function AgentsPage() {
  const [apixClient, setAPXClient] = useState<APXClient | null>(null);
  const [agents, setAgents] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");
  const [activeTab, setActiveTab] = useState("all");
  const [isConnected, setIsConnected] = useState(false);
  const [builderView, setBuilderView] = useState<"form" | "canvas">("form");

  useEffect(() => {
    const initializeConnection = async () => {
      try {
        const apixClient = getAPXClient();
        setAPXClient(apixClient);

        // Subscribe to agent events
        apixClient.subscribe("agent.created", ["agent.created"], handleAgentCreated, {
          filters: {
            organizationId: "org-123",
          },
        });
        apixClient.subscribe("agent.updated", ["agent.updated"], handleAgentUpdated, {
          filters: {
            organizationId: "org-123",
          },
        });
        apixClient.subscribe("agent.deleted", ["agent.deleted"], handleAgentDeleted, {
          filters: {
            organizationId: "org-123",
          },
        });
        apixClient.subscribe("agent.executed", ["agent.executed"], handleAgentExecuted, {
          filters: {
            organizationId: "org-123",
          },
        });

        // Load initial data
        await loadAgents();

        setIsConnected(true);
      } catch (error) {
        console.error("Failed to initialize APIX connection:", error);
        setIsConnected(false);
      } finally {
        setLoading(false);
      }
    };

    initializeConnection();
  }, []);

  const handleAgentCreated = (data: any) => {
    setAgents((prev) => [...prev, data.agent]);
  };

  const handleAgentUpdated = (data: any) => {
    setAgents((prev) =>
      prev.map((agent) =>
        agent.id === data.agent.id ? data.agent : agent
      ),
    );
  };

  const handleAgentDeleted = (data: any) => {
    setAgents((prev) =>
      prev.filter((agent) => agent.id !== data.agent.id)
    );
  };

  const handleAgentExecuted = (data: any) => {
    setAgents((prev) =>
      prev.map((agent) =>
        agent.id === data.agent.id ? data.agent : agent
      ),
    );
  };

  const loadAgents = async () => {
    if (apixClient) {
      apixClient.sendEvent({
        id: "1",
        type: "agent.loaded",
        module: "agents",
        organizationId: "org-123",
        userId: "user-456",
        timestamp: new Date().toISOString(),
        version: 1,
        data: {
          agents: [
            {
              id: "1",
              name: "Customer Support Agent",
              description: "Handles customer inquiries and support tickets",
              status: "active",
              version: "1.2.0",
              lastExecuted: "2023-06-15T10:30:00Z",
              executionCount: 523,
              avgResponseTime: "1.1s",
              cost: "$12.45",
            },
            {
              id: "2",
              name: "Sales Assistant",
              description: "Helps qualify leads and answer product questions",
              status: "active",
              version: "2.0.1",
              lastExecuted: "2023-06-14T16:45:00Z",
              executionCount: 342,
              avgResponseTime: "0.9s",
              cost: "$8.72",
            },
            {
              id: "3",
              name: "Data Analyst",
              description: "Analyzes data and generates reports",
              status: "draft",
              version: "0.5.0",
              lastExecuted: "2023-06-10T09:15:00Z",
              executionCount: 12,
              avgResponseTime: "2.3s",
              cost: "$1.18",
            },
            {
              id: "4",
              name: "Content Generator",
              description: "Creates blog posts and social media content",
              status: "active",
              version: "1.0.0",
              lastExecuted: "2023-06-15T08:20:00Z",
              executionCount: 187,
              avgResponseTime: "1.8s",
              cost: "$5.63",
            },
            {
              id: "5",
              name: "Code Assistant",
              description: "Helps with code generation and debugging",
              status: "archived",
              version: "0.9.2",
              lastExecuted: "2023-05-28T14:10:00Z",
              executionCount: 220,
              avgResponseTime: "1.5s",
              cost: "$7.91",
            },
          ],
        },
      });
    }
  };

  return (
    <div className="p-6 space-y-6">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Agents</h1>
          <p className="text-muted-foreground mt-1">
            Create, manage, and monitor your AI agents
          </p>
        </div>
        <Button className="flex items-center gap-2">
          <Plus className="h-4 w-4" />
          Create Agent
        </Button>
      </div>

      <Tabs defaultValue="all" className="w-full">
        <div className="flex justify-between items-center mb-4">
          <TabsList>
            <TabsTrigger value="all">All Agents</TabsTrigger>
            <TabsTrigger value="active">Active</TabsTrigger>
            <TabsTrigger value="draft">Drafts</TabsTrigger>
            <TabsTrigger value="archived">Archived</TabsTrigger>
            <TabsTrigger value="builder">Builder</TabsTrigger>
            <TabsTrigger value="canvas">Canvas Builder</TabsTrigger>
            <TabsTrigger value="workflow">Workflow Example</TabsTrigger>
          </TabsList>
          <div className="flex items-center gap-2">
            <div className="relative">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                type="search"
                placeholder="Search agents..."
                className="pl-8 w-[250px]"
              />
            </div>
            <Button variant="outline" size="icon">
              <Filter className="h-4 w-4" />
            </Button>
          </div>
        </div>

        <TabsContent value="all" className="mt-0">
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 mb-8">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle>Total Agents</CardTitle>
                <CardDescription>Across all statuses</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold">24</div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle>Active Agents</CardTitle>
                <CardDescription>Currently in production</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold">18</div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle>Total Executions</CardTitle>
                <CardDescription>Last 30 days</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold">1,284</div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle>Avg. Response Time</CardTitle>
                <CardDescription>Last 30 days</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold">1.2s</div>
              </CardContent>
            </Card>
          </div>

          <AgentList
              agents={agents}
          />
        </TabsContent>

        <TabsContent value="active">
          <AgentList
            agents={agents.filter(agent => agent.status === 'active')}
          />
        </TabsContent>

        <TabsContent value="draft">
          <AgentList
            agents={agents.filter(agent => agent.status === 'draft')}
          />
        </TabsContent>

        <TabsContent value="archived">
          <AgentList
            agents={agents.filter(agent => agent.status === 'archived')}
          />
        </TabsContent>

        <TabsContent value="builder">
          <div className="mb-4">
            <Card>
              <CardHeader>
                <CardTitle>Agent Builder</CardTitle>
                <CardDescription>
                  Create and configure your AI agent using the form-based builder
                </CardDescription>
              </CardHeader>
              <CardContent>
                <AgentBuilder />
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="canvas">
          <div className="mb-4">
            <Card>
              <CardHeader className="pb-2">
                <div className="flex justify-between items-center">
                  <div>
                    <CardTitle className="flex items-center gap-2">
                      <Workflow className="h-5 w-5 text-primary" />
                      Visual Agent Canvas Builder
                    </CardTitle>
                    <CardDescription>
                      Create and configure your AI agent using a visual drag-and-drop interface
                    </CardDescription>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="p-0">
                <div className="h-[800px] border-t">
                  <AgentCanvasBuilder />
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="workflow">
          <div className="mb-4">
            <Card>
              <CardHeader className="pb-2">
                <div className="flex justify-between items-center">
                  <div>
                    <CardTitle className="flex items-center gap-2">
                      <Workflow className="h-5 w-5 text-primary" />
                      Complex Agent Workflow Example
                    </CardTitle>
                    <CardDescription>
                      An example of a complex agent workflow with multiple nodes and connections
                    </CardDescription>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="p-0">
                <div className="h-[800px] border-t">
                  <ComplexAgentWorkflow />
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}