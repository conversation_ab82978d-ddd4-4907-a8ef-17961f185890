"use client";

import React, { useState, useC<PERSON>back, useRef } from 'react';
import { 
  ReactFlow, 
  ReactFlowProvider, 
  Background, 
  Controls, 
  MiniMap, 
  Panel, 
  useNodesState, 
  useEdgesState, 
  addEdge, 
  Connection, 
  Edge, 
  Node, 
  NodeTypes, 
  EdgeTypes,
  Position
} from 'reactflow';
import { 
  Card, 
  CardContent, 
  CardHeader, 
  CardTitle, 
  CardDescription 
} from "@/components/ui/card";
import { 
  <PERSON><PERSON>, 
  <PERSON><PERSON><PERSON>ontent, 
  Ta<PERSON>List, 
  TabsTrigger 
} from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { ScrollArea } from "@/components/ui/scroll-area";
import { 
  Bot, 
  Database, 
  Zap, 
  Search, 
  MessageSquare, 
  FileText, 
  Settings, 
  Plus, 
  Trash2, 
  Save, 
  Play, 
  Pause, 
  Copy, 
  Download, 
  Upload, 
  Layers, 
  <PERSON>R<PERSON>, 
  Alert<PERSON>ircle, 
  Check, 
  X, 
  Workflow
} from "lucide-react";

// Import custom node components
import AgentNode from './nodes/AgentNode';
import ToolNode from './nodes/ToolNode';
import KnowledgeNode from './nodes/KnowledgeNode';
import InputNode from './nodes/InputNode';
import OutputNode from './nodes/OutputNode';
import CustomEdge from './edges/CustomEdge';

// Import CSS
import '../../styles/reactflow.css';

// Define node types
const nodeTypes: NodeTypes = {
  agent: AgentNode,
  tool: ToolNode,
  knowledge: KnowledgeNode,
  input: InputNode,
  output: OutputNode
};

// Define edge types
const edgeTypes: EdgeTypes = {
  custom: CustomEdge
};

// Initial nodes and edges
const initialNodes: Node[] = [
  {
    id: 'input-1',
    type: 'input',
    data: { 
      label: 'User Input',
      description: 'Starting point for user queries',
      icon: <MessageSquare className="h-4 w-4" />
    },
    position: { x: 100, y: 100 },
    sourcePosition: Position.Right
  },
  {
    id: 'agent-1',
    type: 'agent',
    data: { 
      label: 'Main Agent',
      description: 'Processes user queries and orchestrates responses',
      model: 'gpt-4',
      temperature: 0.7,
      icon: <Bot className="h-4 w-4" />
    },
    position: { x: 350, y: 100 },
    targetPosition: Position.Left,
    sourcePosition: Position.Right
  },
  {
    id: 'output-1',
    type: 'output',
    data: { 
      label: 'Response',
      description: 'Final output to the user',
      icon: <MessageSquare className="h-4 w-4" />
    },
    position: { x: 600, y: 100 },
    targetPosition: Position.Left
  }
];

const initialEdges: Edge[] = [
  {
    id: 'edge-input-agent',
    source: 'input-1',
    target: 'agent-1',
    type: 'custom',
    data: { label: 'Query' }
  },
  {
    id: 'edge-agent-output',
    source: 'agent-1',
    target: 'output-1',
    type: 'custom',
    data: { label: 'Response' }
  }
];

// Node palette items
const nodePaletteItems = [
  {
    type: 'agent',
    label: 'Agent',
    description: 'AI agent with configurable parameters',
    icon: <Bot className="h-4 w-4" />
  },
  {
    type: 'tool',
    label: 'Tool',
    description: 'External tool or API integration',
    icon: <Zap className="h-4 w-4" />
  },
  {
    type: 'knowledge',
    label: 'Knowledge Base',
    description: 'Vector database for information retrieval',
    icon: <Database className="h-4 w-4" />
  },
  {
    type: 'input',
    label: 'Input',
    description: 'Entry point for workflow',
    icon: <MessageSquare className="h-4 w-4" />
  },
  {
    type: 'output',
    label: 'Output',
    description: 'Final response or action',
    icon: <MessageSquare className="h-4 w-4" />
  }
];

const AgentCanvasBuilder = () => {
  // State for nodes and edges
  const [nodes, setNodes, onNodesChange] = useNodesState(initialNodes);
  const [edges, setEdges, onEdgesChange] = useEdgesState(initialEdges);
  const [selectedNode, setSelectedNode] = useState<Node | null>(null);
  const [selectedEdge, setSelectedEdge] = useState<Edge | null>(null);
  const [isDragging, setIsDragging] = useState(false);
  const [canvasName, setCanvasName] = useState('New Agent Configuration');
  const reactFlowWrapper = useRef<HTMLDivElement>(null);
  const [reactFlowInstance, setReactFlowInstance] = useState<any>(null);

  // Handle connections between nodes
  const onConnect = useCallback((connection: Connection) => {
    setEdges((eds) => addEdge({
      ...connection,
      type: 'custom',
      data: { label: 'Connection' }
    }, eds));
  }, [setEdges]);

  // Handle node click
  const onNodeClick = useCallback((event: React.MouseEvent, node: Node) => {
    setSelectedNode(node);
    setSelectedEdge(null);
  }, []);

  // Handle edge click
  const onEdgeClick = useCallback((event: React.MouseEvent, edge: Edge) => {
    setSelectedEdge(edge);
    setSelectedNode(null);
  }, []);

  // Handle background click
  const onPaneClick = useCallback(() => {
    setSelectedNode(null);
    setSelectedEdge(null);
  }, []);

  // Handle node drag start
  const onNodeDragStart = useCallback(() => {
    setIsDragging(true);
  }, []);

  // Handle node drag stop
  const onNodeDragStop = useCallback(() => {
    setIsDragging(false);
  }, []);

  // Handle drag over for node palette items
  const onDragOver = useCallback((event: React.DragEvent) => {
    event.preventDefault();
    event.dataTransfer.dropEffect = 'move';
  }, []);

  // Handle drop for node palette items
  const onDrop = useCallback(
    (event: React.DragEvent) => {
      event.preventDefault();

      const type = event.dataTransfer.getData('application/reactflow/type');
      const label = event.dataTransfer.getData('application/reactflow/label');
      const icon = event.dataTransfer.getData('application/reactflow/icon');
      const description = event.dataTransfer.getData('application/reactflow/description');

      if (!type || !reactFlowInstance) return;

      const position = reactFlowInstance.screenToFlowPosition({
        x: event.clientX,
        y: event.clientY,
      });

      const newNode: Node = {
        id: `${type}-${Date.now()}`,
        type,
        position,
        data: { 
          label: label || `New ${type}`,
          description: description || '',
          icon: icon || <Settings className="h-4 w-4" />
        },
        sourcePosition: Position.Right,
        targetPosition: Position.Left
      };

      setNodes((nds) => nds.concat(newNode));
    },
    [reactFlowInstance, setNodes]
  );

  // Handle drag start for node palette items
  const onDragStart = (event: React.DragEvent, nodeType: string, nodeLabel: string, nodeDescription: string) => {
    event.dataTransfer.setData('application/reactflow/type', nodeType);
    event.dataTransfer.setData('application/reactflow/label', nodeLabel);
    event.dataTransfer.setData('application/reactflow/description', nodeDescription);
    event.dataTransfer.effectAllowed = 'move';
  };

  // Update node data
  const updateNodeData = (id: string, newData: any) => {
    setNodes((nds) =>
      nds.map((node) => {
        if (node.id === id) {
          return { ...node, data: { ...node.data, ...newData } };
        }
        return node;
      })
    );
  };

  // Delete selected node or edge
  const deleteSelected = () => {
    if (selectedNode) {
      setNodes((nds) => nds.filter((node) => node.id !== selectedNode.id));
      setSelectedNode(null);
    }
    if (selectedEdge) {
      setEdges((eds) => eds.filter((edge) => edge.id !== selectedEdge.id));
      setSelectedEdge(null);
    }
  };

  // Save configuration
  const saveConfiguration = () => {
    const config = {
      name: canvasName,
      nodes,
      edges,
      timestamp: new Date().toISOString()
    };
    console.log('Saving configuration:', config);
    // In a real app, you would save this to your backend
    alert('Configuration saved successfully!');
  };

  // Export configuration
  const exportConfiguration = () => {
    const config = {
      name: canvasName,
      nodes,
      edges,
      timestamp: new Date().toISOString()
    };
    const blob = new Blob([JSON.stringify(config, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `${canvasName.replace(/\s+/g, '-').toLowerCase()}.json`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  // Import configuration
  const importConfiguration = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const config = JSON.parse(e.target?.result as string);
        setCanvasName(config.name || 'Imported Configuration');
        setNodes(config.nodes || []);
        setEdges(config.edges || []);
      } catch (error) {
        console.error('Error importing configuration:', error);
        alert('Invalid configuration file');
      }
    };
    reader.readAsText(file);
  };

  return (
    <div className="flex flex-col h-full">
      <div className="flex items-center justify-between p-4 border-b">
        <div className="flex items-center gap-2">
          <Workflow className="h-5 w-5 text-primary" />
          <Input 
            value={canvasName} 
            onChange={(e) => setCanvasName(e.target.value)} 
            className="font-medium text-lg border-none focus-visible:ring-0 focus-visible:ring-offset-0 w-[300px]"
          />
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm" onClick={exportConfiguration}>
            <Download className="h-4 w-4 mr-1" />
            Export
          </Button>
          <div className="relative">
            <Button variant="outline" size="sm">
              <Upload className="h-4 w-4 mr-1" />
              Import
              <Input 
                type="file" 
                accept=".json" 
                onChange={importConfiguration} 
                className="absolute inset-0 opacity-0 cursor-pointer" 
              />
            </Button>
          </div>
          <Button variant="default" size="sm" onClick={saveConfiguration}>
            <Save className="h-4 w-4 mr-1" />
            Save
          </Button>
        </div>
      </div>

      <div className="flex flex-1 overflow-hidden">
        {/* Node palette */}
        <div className="w-64 border-r p-4 bg-muted/20">
          <h3 className="font-medium mb-3">Node Palette</h3>
          <div className="space-y-2">
            {nodePaletteItems.map((item, index) => (
              <div
                key={index}
                className="node-palette-item flex items-center gap-2 p-3 bg-card border rounded-md cursor-grab hover:border-primary/50 transition-all"
                draggable
                onDragStart={(event) => onDragStart(event, item.type, item.label, item.description)}
              >
                <div className={`p-2 rounded-md ${
                  item.type === 'agent' ? 'bg-blue-100 text-blue-600' :
                  item.type === 'tool' ? 'bg-purple-100 text-purple-600' :
                  item.type === 'knowledge' ? 'bg-green-100 text-green-600' :
                  item.type === 'input' ? 'bg-orange-100 text-orange-600' :
                  'bg-gray-100 text-gray-600'
                }`}>
                  {item.icon}
                </div>
                <div>
                  <p className="text-sm font-medium">{item.label}</p>
                  <p className="text-xs text-muted-foreground">{item.description}</p>
                </div>
              </div>
            ))}
          </div>

          <Separator className="my-4" />

          <div className="space-y-4">
            <h3 className="font-medium">Actions</h3>
            <div className="grid grid-cols-2 gap-2">
              <Button variant="outline" size="sm" onClick={deleteSelected} disabled={!selectedNode && !selectedEdge}>
                <Trash2 className="h-4 w-4 mr-1" />
                Delete
              </Button>
              <Button variant="outline" size="sm" onClick={() => setNodes([])} disabled={nodes.length === 0}>
                <X className="h-4 w-4 mr-1" />
                Clear
              </Button>
              <Button variant="outline" size="sm">
                <Play className="h-4 w-4 mr-1" />
                Test
              </Button>
              <Button variant="outline" size="sm">
                <Copy className="h-4 w-4 mr-1" />
                Clone
              </Button>
            </div>
          </div>
        </div>

        {/* Main canvas */}
        <div className="flex-1 flex flex-col">
          <div className="flex-1" ref={reactFlowWrapper}>
            <ReactFlowProvider>
              <ReactFlow
                nodes={nodes}
                edges={edges}
                onNodesChange={onNodesChange}
                onEdgesChange={onEdgesChange}
                onConnect={onConnect}
                onNodeClick={onNodeClick}
                onEdgeClick={onEdgeClick}
                onPaneClick={onPaneClick}
                onNodeDragStart={onNodeDragStart}
                onNodeDragStop={onNodeDragStop}
                onInit={setReactFlowInstance}
                onDrop={onDrop}
                onDragOver={onDragOver}
                nodeTypes={nodeTypes}
                edgeTypes={edgeTypes}
                fitView
                snapToGrid
                snapGrid={[15, 15]}
                defaultEdgeOptions={{
                  type: 'custom',
                  animated: true
                }}
              >
                <Background variant="dots" gap={12} size={1} />
                <Controls />
                <MiniMap 
                  nodeStrokeColor={(n) => {
                    if (n.type === 'agent') return '#3b82f6';
                    if (n.type === 'tool') return '#8b5cf6';
                    if (n.type === 'knowledge') return '#10b981';
                    if (n.type === 'input') return '#f97316';
                    if (n.type === 'output') return '#f97316';
                    return '#64748b';
                  }}
                  nodeColor={(n) => {
                    if (n.type === 'agent') return '#dbeafe';
                    if (n.type === 'tool') return '#ede9fe';
                    if (n.type === 'knowledge') return '#d1fae5';
                    if (n.type === 'input') return '#ffedd5';
                    if (n.type === 'output') return '#ffedd5';
                    return '#f1f5f9';
                  }}
                />
                <Panel position="top-right">
                  <div className="flex items-center gap-2 bg-white p-2 rounded-md shadow-sm border">
                    <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
                      {nodes.length} Nodes
                    </Badge>
                    <Badge variant="outline" className="bg-purple-50 text-purple-700 border-purple-200">
                      {edges.length} Connections
                    </Badge>
                  </div>
                </Panel>
              </ReactFlow>
            </ReactFlowProvider>
          </div>
        </div>

        {/* Properties panel */}
        <div className="w-80 border-l p-4 bg-muted/20">
          <Tabs defaultValue="properties">
            <TabsList className="grid grid-cols-2 mb-4">
              <TabsTrigger value="properties">Properties</TabsTrigger>
              <TabsTrigger value="settings">Settings</TabsTrigger>
            </TabsList>
            <TabsContent value="properties" className="space-y-4">
              {!selectedNode && !selectedEdge ? (
                <div className="flex flex-col items-center justify-center h-[400px] text-center p-4 border-2 border-dashed rounded-md">
                  <Settings className="h-10 w-10 text-muted-foreground mb-2" />
                  <h3 className="font-medium">No Element Selected</h3>
                  <p className="text-sm text-muted-foreground mt-1">
                    Select a node or edge to view and edit its properties
                  </p>
                </div>
              ) : selectedNode ? (
                <Card>
                  <CardHeader className="pb-2">
                    <div className="flex items-center justify-between">
                      <CardTitle className="text-lg">Node Properties</CardTitle>
                      <Badge>{selectedNode.type}</Badge>
                    </div>
                    <CardDescription>
                      Configure the selected node
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="node-label">Label</Label>
                      <Input
                        id="node-label"
                        value={selectedNode.data.label || ''}
                        onChange={(e) => updateNodeData(selectedNode.id, { label: e.target.value })}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="node-description">Description</Label>
                      <Input
                        id="node-description"
                        value={selectedNode.data.description || ''}
                        onChange={(e) => updateNodeData(selectedNode.id, { description: e.target.value })}
                      />
                    </div>

                    {selectedNode.type === 'agent' && (
                      <>
                        <div className="space-y-2">
                          <Label htmlFor="agent-model">Model</Label>
                          <select
                            id="agent-model"
                            className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                            value={selectedNode.data.model || 'gpt-4'}
                            onChange={(e) => updateNodeData(selectedNode.id, { model: e.target.value })}
                          >
                            <option value="gpt-4">GPT-4</option>
                            <option value="gpt-3.5-turbo">GPT-3.5 Turbo</option>
                            <option value="claude-3-opus">Claude 3 Opus</option>
                            <option value="claude-3-sonnet">Claude 3 Sonnet</option>
                            <option value="gemini-pro">Gemini Pro</option>
                            <option value="mistral-large">Mistral Large</option>
                          </select>
                        </div>
                        <div className="space-y-2">
                          <div className="flex items-center justify-between">
                            <Label htmlFor="agent-temperature">Temperature: {selectedNode.data.temperature || 0.7}</Label>
                          </div>
                          <Input
                            id="agent-temperature"
                            type="range"
                            min="0"
                            max="2"
                            step="0.1"
                            value={selectedNode.data.temperature || 0.7}
                            onChange={(e) => updateNodeData(selectedNode.id, { temperature: parseFloat(e.target.value) })}
                          />
                          <div className="flex justify-between text-xs text-muted-foreground">
                            <span>Precise</span>
                            <span>Balanced</span>
                            <span>Creative</span>
                          </div>
                        </div>
                      </>
                    )}

                    {selectedNode.type === 'tool' && (
                      <>
                        <div className="space-y-2">
                          <Label htmlFor="tool-type">Tool Type</Label>
                          <select
                            id="tool-type"
                            className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                            value={selectedNode.data.toolType || 'api'}
                            onChange={(e) => updateNodeData(selectedNode.id, { toolType: e.target.value })}
                          >
                            <option value="api">API</option>
                            <option value="function">Function</option>
                            <option value="database">Database</option>
                            <option value="search">Search</option>
                          </select>
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="tool-endpoint">Endpoint/Function</Label>
                          <Input
                            id="tool-endpoint"
                            value={selectedNode.data.endpoint || ''}
                            onChange={(e) => updateNodeData(selectedNode.id, { endpoint: e.target.value })}
                            placeholder="API endpoint or function name"
                          />
                        </div>
                      </>
                    )}

                    {selectedNode.type === 'knowledge' && (
                      <>
                        <div className="space-y-2">
                          <Label htmlFor="knowledge-source">Knowledge Source</Label>
                          <select
                            id="knowledge-source"
                            className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                            value={selectedNode.data.source || 'vector-db'}
                            onChange={(e) => updateNodeData(selectedNode.id, { source: e.target.value })}
                          >
                            <option value="vector-db">Vector Database</option>
                            <option value="document">Document Collection</option>
                            <option value="api">External API</option>
                          </select>
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="knowledge-similarity">Similarity Threshold</Label>
                          <Input
                            id="knowledge-similarity"
                            type="range"
                            min="0.1"
                            max="1"
                            step="0.05"
                            value={selectedNode.data.similarity || 0.7}
                            onChange={(e) => updateNodeData(selectedNode.id, { similarity: parseFloat(e.target.value) })}
                          />
                          <div className="flex justify-between text-xs text-muted-foreground">
                            <span>0.1</span>
                            <span>0.5</span>
                            <span>1.0</span>
                          </div>
                        </div>
                      </>
                    )}
                  </CardContent>
                </Card>
              ) : (
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-lg">Edge Properties</CardTitle>
                    <CardDescription>
                      Configure the selected connection
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="edge-label">Label</Label>
                      <Input
                        id="edge-label"
                        value={selectedEdge?.data?.label || ''}
                        onChange={(e) => {
                          setEdges((eds) =>
                            eds.map((edge) => {
                              if (edge.id === selectedEdge?.id) {
                                return {
                                  ...edge,
                                  data: { ...edge.data, label: e.target.value }
                                };
                              }
                              return edge;
                            })
                          );
                        }}
                      />
                    </div>
                    <div className="flex items-center gap-2 text-sm">
                      <span className="text-muted-foreground">From:</span>
                      <Badge variant="outline">{selectedEdge?.source}</Badge>
                      <ArrowRight className="h-4 w-4 text-muted-foreground" />
                      <Badge variant="outline">{selectedEdge?.target}</Badge>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="edge-animated">Animation</Label>
                      <div className="flex items-center space-x-2">
                        <input
                          id="edge-animated"
                          type="checkbox"
                          checked={selectedEdge?.animated || false}
                          onChange={(e) => {
                            setEdges((eds) =>
                              eds.map((edge) => {
                                if (edge.id === selectedEdge?.id) {
                                  return { ...edge, animated: e.target.checked };
                                }
                                return edge;
                              })
                            );
                          }}
                          className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
                        />
                        <Label htmlFor="edge-animated" className="text-sm font-normal">
                          Enable animation
                        </Label>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}
            </TabsContent>
            <TabsContent value="settings" className="space-y-4">
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-lg">Canvas Settings</CardTitle>
                  <CardDescription>
                    Configure the canvas behavior
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="canvas-name">Canvas Name</Label>
                    <Input
                      id="canvas-name"
                      value={canvasName}
                      onChange={(e) => setCanvasName(e.target.value)}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label>Display Options</Label>
                    <div className="grid grid-cols-2 gap-2">
                      <div className="flex items-center space-x-2">
                        <input
                          id="show-grid"
                          type="checkbox"
                          defaultChecked
                          className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
                        />
                        <Label htmlFor="show-grid" className="text-sm font-normal">
                          Show grid
                        </Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <input
                          id="show-minimap"
                          type="checkbox"
                          defaultChecked
                          className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
                        />
                        <Label htmlFor="show-minimap" className="text-sm font-normal">
                          Show minimap
                        </Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <input
                          id="snap-to-grid"
                          type="checkbox"
                          defaultChecked
                          className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
                        />
                        <Label htmlFor="snap-to-grid" className="text-sm font-normal">
                          Snap to grid
                        </Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <input
                          id="animate-connections"
                          type="checkbox"
                          defaultChecked
                          className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
                        />
                        <Label htmlFor="animate-connections" className="text-sm font-normal">
                          Animate connections
                        </Label>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-lg">Execution Settings</CardTitle>
                  <CardDescription>
                    Configure how the agent workflow executes
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="execution-mode">Execution Mode</Label>
                    <select
                      id="execution-mode"
                      className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                      defaultValue="sequential"
                    >
                      <option value="sequential">Sequential</option>
                      <option value="parallel">Parallel (when possible)</option>
                      <option value="conditional">Conditional</option>
                    </select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="timeout">Execution Timeout (seconds)</Label>
                    <Input
                      id="timeout"
                      type="number"
                      min="1"
                      max="300"
                      defaultValue="60"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label>Error Handling</Label>
                    <div className="grid grid-cols-2 gap-2">
                      <div className="flex items-center space-x-2">
                        <input
                          id="retry-on-error"
                          type="checkbox"
                          defaultChecked
                          className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
                        />
                        <Label htmlFor="retry-on-error" className="text-sm font-normal">
                          Retry on error
                        </Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <input
                          id="fail-fast"
                          type="checkbox"
                          className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
                        />
                        <Label htmlFor="fail-fast" className="text-sm font-normal">
                          Fail fast
                        </Label>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  );
};

export default AgentCanvasBuilder;