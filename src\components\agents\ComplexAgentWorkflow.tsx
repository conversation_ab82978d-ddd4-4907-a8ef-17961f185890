"use client";

import React, { useState, useCallback } from 'react';
import { 
  <PERSON>act<PERSON>low, 
  ReactFlowProvider, 
  Background, 
  Controls, 
  MiniMap, 
  Panel, 
  useNodesState, 
  useEdgesState, 
  addEdge, 
  Connection, 
  Edge, 
  Node, 
  NodeTypes, 
  EdgeTypes,
  Position
} from 'reactflow';
import { 
  Card, 
  CardContent, 
  CardHeader, 
  CardTitle, 
  CardDescription 
} from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { 
  Bot, 
  Database, 
  Zap, 
  MessageSquare, 
  FileText, 
  Search, 
  Play, 
  Pause, 
  Save, 
  Download, 
  Upload, 
  Workflow
} from "lucide-react";

// Import custom node components
import AgentNode from '@/components/agents/nodes/AgentNode';
import ToolNode from '@/components/agents/nodes/ToolNode';
import KnowledgeNode from '@/components/agents/nodes/KnowledgeNode';
import InputNode from '@/components/agents/nodes/InputNode';
import OutputNode from '@/components/agents/nodes/OutputNode';
import CustomEdge from '@/components/agents/edges/CustomEdge';

// Import CSS
import '@/styles/reactflow.css';

// Define node types
const nodeTypes: NodeTypes = {
  agent: AgentNode,
  tool: ToolNode,
  knowledge: KnowledgeNode,
  input: InputNode,
  output: OutputNode
};

// Define edge types
const edgeTypes: EdgeTypes = {
  custom: CustomEdge
};

// Complex workflow example with multiple nodes and connections
const initialNodes: Node[] = [
  {
    id: 'input-1',
    type: 'input',
    data: { 
      label: 'User Query',
      description: 'Starting point for user queries',
      icon: <MessageSquare className="h-4 w-4" />
    },
    position: { x: 100, y: 250 },
    sourcePosition: Position.Right
  },
  {
    id: 'agent-router',
    type: 'agent',
    data: { 
      label: 'Query Router',
      description: 'Routes queries to appropriate agents',
      model: 'gpt-3.5-turbo',
      temperature: 0.3,
      icon: <Bot className="h-4 w-4" />
    },
    position: { x: 350, y: 250 },
    targetPosition: Position.Left,
    sourcePosition: Position.Right
  },
  {
    id: 'knowledge-base',
    type: 'knowledge',
    data: { 
      label: 'Company Knowledge',
      description: 'Vector database with company information',
      source: 'Vector Database',
      similarity: 0.75,
      icon: <Database className="h-4 w-4" />
    },
    position: { x: 350, y: 100 },
    targetPosition: Position.Left,
    sourcePosition: Position.Right
  },
  {
    id: 'agent-support',
    type: 'agent',
    data: { 
      label: 'Support Agent',
      description: 'Handles customer support queries',
      model: 'claude-3-sonnet',
      temperature: 0.5,
      icon: <Bot className="h-4 w-4" />
    },
    position: { x: 650, y: 100 },
    targetPosition: Position.Left,
    sourcePosition: Position.Right
  },
  {
    id: 'agent-sales',
    type: 'agent',
    data: { 
      label: 'Sales Agent',
      description: 'Handles sales and pricing queries',
      model: 'gpt-4',
      temperature: 0.7,
      icon: <Bot className="h-4 w-4" />
    },
    position: { x: 650, y: 250 },
    targetPosition: Position.Left,
    sourcePosition: Position.Right
  },
  {
    id: 'agent-technical',
    type: 'agent',
    data: { 
      label: 'Technical Agent',
      description: 'Handles technical queries',
      model: 'mistral-large',
      temperature: 0.4,
      icon: <Bot className="h-4 w-4" />
    },
    position: { x: 650, y: 400 },
    targetPosition: Position.Left,
    sourcePosition: Position.Right
  },
  {
    id: 'tool-crm',
    type: 'tool',
    data: { 
      label: 'CRM API',
      description: 'Customer data lookup',
      toolType: 'API',
      endpoint: 'https://api.crm.example.com/v1/customers',
      icon: <Zap className="h-4 w-4" />
    },
    position: { x: 950, y: 175 },
    targetPosition: Position.Left,
    sourcePosition: Position.Right
  },
  {
    id: 'tool-docs',
    type: 'tool',
    data: { 
      label: 'Documentation Search',
      description: 'Technical documentation search',
      toolType: 'Search',
      endpoint: 'https://docs.example.com/api/search',
      icon: <Search className="h-4 w-4" />
    },
    position: { x: 950, y: 400 },
    targetPosition: Position.Left,
    sourcePosition: Position.Right
  },
  {
    id: 'output-1',
    type: 'output',
    data: { 
      label: 'Response',
      description: 'Final response to the user',
      icon: <MessageSquare className="h-4 w-4" />
    },
    position: { x: 1200, y: 250 },
    targetPosition: Position.Left
  }
];

const initialEdges: Edge[] = [
  {
    id: 'edge-input-router',
    source: 'input-1',
    target: 'agent-router',
    type: 'custom',
    data: { label: 'Query' }
  },
  {
    id: 'edge-kb-router',
    source: 'knowledge-base',
    target: 'agent-router',
    type: 'custom',
    data: { label: 'Context' }
  },
  {
    id: 'edge-router-support',
    source: 'agent-router',
    target: 'agent-support',
    type: 'custom',
    data: { label: 'Support Query' }
  },
  {
    id: 'edge-router-sales',
    source: 'agent-router',
    target: 'agent-sales',
    type: 'custom',
    data: { label: 'Sales Query' }
  },
  {
    id: 'edge-router-technical',
    source: 'agent-router',
    target: 'agent-technical',
    type: 'custom',
    data: { label: 'Technical Query' }
  },
  {
    id: 'edge-support-crm',
    source: 'agent-support',
    target: 'tool-crm',
    type: 'custom',
    data: { label: 'Customer Lookup' }
  },
  {
    id: 'edge-sales-crm',
    source: 'agent-sales',
    target: 'tool-crm',
    type: 'custom',
    data: { label: 'Customer Lookup' }
  },
  {
    id: 'edge-technical-docs',
    source: 'agent-technical',
    target: 'tool-docs',
    type: 'custom',
    data: { label: 'Documentation Search' }
  },
  {
    id: 'edge-support-output',
    source: 'agent-support',
    target: 'output-1',
    type: 'custom',
    data: { label: 'Response' }
  },
  {
    id: 'edge-sales-output',
    source: 'agent-sales',
    target: 'output-1',
    type: 'custom',
    data: { label: 'Response' }
  },
  {
    id: 'edge-technical-output',
    source: 'agent-technical',
    target: 'output-1',
    type: 'custom',
    data: { label: 'Response' }
  }
];

const ComplexAgentWorkflow = () => {
  // State for nodes and edges
  const [nodes, setNodes, onNodesChange] = useNodesState(initialNodes);
  const [edges, setEdges, onEdgesChange] = useEdgesState(initialEdges);
  const [isRunning, setIsRunning] = useState(false);
  const [activeNodeId, setActiveNodeId] = useState<string | null>(null);
  const [activeEdgeId, setActiveEdgeId] = useState<string | null>(null);
  const [executionPath, setExecutionPath] = useState<string[]>([]);

  // Handle connections between nodes
  const onConnect = useCallback((connection: Connection) => {
    setEdges((eds) => addEdge({
      ...connection,
      type: 'custom',
      data: { label: 'Connection' }
    }, eds));
  }, [setEdges]);

  // Simulate workflow execution
  const runWorkflow = () => {
    setIsRunning(true);
    setExecutionPath([]);
    
    // Reset all nodes and edges to default state
    setNodes((nds) => nds.map(node => ({
      ...node,
      data: {
        ...node.data,
        status: undefined
      }
    })));
    
    setEdges((eds) => eds.map(edge => ({
      ...edge,
      animated: false,
      style: { ...edge.style }
    })));
    
    // Start execution from input node
    const inputNode = nodes.find(node => node.type === 'input');
    if (inputNode) {
      simulateNodeExecution(inputNode.id);
    }
  };

  // Simulate execution of a specific node
  const simulateNodeExecution = (nodeId: string) => {
    // Mark node as active
    setActiveNodeId(nodeId);
    setExecutionPath(prev => [...prev, nodeId]);
    
    // Update node status
    setNodes((nds) => nds.map(node => {
      if (node.id === nodeId) {
        return {
          ...node,
          data: {
            ...node.data,
            status: 'running'
          }
        };
      }
      return node;
    }));
    
    // Find outgoing edges
    const outgoingEdges = edges.filter(edge => edge.source === nodeId);
    
    // Simulate processing time
    setTimeout(() => {
      // Mark node as completed
      setNodes((nds) => nds.map(node => {
        if (node.id === nodeId) {
          return {
            ...node,
            data: {
              ...node.data,
              status: 'completed'
            }
          };
        }
        return node;
      }));
      
      // If no outgoing edges, we're done
      if (outgoingEdges.length === 0) {
        if (nodeId.startsWith('output')) {
          setTimeout(() => {
            setIsRunning(false);
            setActiveNodeId(null);
            setActiveEdgeId(null);
          }, 1000);
        }
        return;
      }
      
      // Process each outgoing edge
      outgoingEdges.forEach((edge, index) => {
        // Animate the edge
        setActiveEdgeId(edge.id);
        setEdges((eds) => eds.map(e => {
          if (e.id === edge.id) {
            return {
              ...e,
              animated: true,
              style: { 
                ...e.style,
                stroke: '#3b82f6',
                strokeWidth: 3
              }
            };
          }
          return e;
        }));
        
        // After a delay, process the target node
        setTimeout(() => {
          simulateNodeExecution(edge.target);
        }, 1000 + index * 500);
      });
    }, 1500);
  };

  return (
    <div className="flex flex-col h-full">
      <div className="flex items-center justify-between p-4 border-b">
        <div className="flex items-center gap-2">
          <Workflow className="h-5 w-5 text-primary" />
          <h2 className="font-medium text-lg">Complex Agent Workflow Example</h2>
        </div>
        <div className="flex items-center gap-2">
          <Button 
            variant={isRunning ? "outline" : "default"} 
            size="sm" 
            onClick={isRunning ? () => setIsRunning(false) : runWorkflow}
            disabled={isRunning && activeNodeId !== null}
          >
            {isRunning ? (
              <>
                <Pause className="h-4 w-4 mr-1" />
                Stop
              </>
            ) : (
              <>
                <Play className="h-4 w-4 mr-1" />
                Run Workflow
              </>
            )}
          </Button>
          <Button variant="outline" size="sm">
            <Save className="h-4 w-4 mr-1" />
            Save
          </Button>
        </div>
      </div>

      <div className="flex flex-1 overflow-hidden">
        {/* Main canvas */}
        <div className="flex-1">
          <ReactFlowProvider>
            <ReactFlow
              nodes={nodes}
              edges={edges}
              onNodesChange={onNodesChange}
              onEdgesChange={onEdgesChange}
              onConnect={onConnect}
              nodeTypes={nodeTypes}
              edgeTypes={edgeTypes}
              fitView
              attributionPosition="bottom-right"
            >
              <Background variant="dots" gap={12} size={1} />
              <Controls />
              <MiniMap 
                nodeStrokeColor={(n) => {
                  if (n.type === 'agent') return '#3b82f6';
                  if (n.type === 'tool') return '#8b5cf6';
                  if (n.type === 'knowledge') return '#10b981';
                  if (n.type === 'input') return '#f97316';
                  if (n.type === 'output') return '#f97316';
                  return '#64748b';
                }}
                nodeColor={(n) => {
                  if (n.type === 'agent') return '#dbeafe';
                  if (n.type === 'tool') return '#ede9fe';
                  if (n.type === 'knowledge') return '#d1fae5';
                  if (n.type === 'input') return '#ffedd5';
                  if (n.type === 'output') return '#ffedd5';
                  return '#f1f5f9';
                }}
              />
              <Panel position="top-right">
                <div className="flex items-center gap-2 bg-white p-2 rounded-md shadow-sm border">
                  <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
                    {nodes.length} Nodes
                  </Badge>
                  <Badge variant="outline" className="bg-purple-50 text-purple-700 border-purple-200">
                    {edges.length} Connections
                  </Badge>
                </div>
              </Panel>
              {isRunning && (
                <Panel position="bottom-center">
                  <Card className="w-[400px] shadow-lg border-primary/20">
                    <CardHeader className="py-3">
                      <CardTitle className="text-sm font-medium">Workflow Execution</CardTitle>
                      <CardDescription className="text-xs">
                        {activeNodeId ? `Currently processing: ${activeNodeId}` : 'Completed'}
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="py-2">
                      <div className="text-xs space-y-1">
                        {executionPath.map((nodeId, index) => {
                          const node = nodes.find(n => n.id === nodeId);
                          return (
                            <div key={index} className="flex items-center gap-2">
                              <Badge variant="outline" className={`
                                ${node?.type === 'agent' ? 'bg-blue-50 text-blue-700 border-blue-200' : ''}
                                ${node?.type === 'tool' ? 'bg-purple-50 text-purple-700 border-purple-200' : ''}
                                ${node?.type === 'knowledge' ? 'bg-green-50 text-green-700 border-green-200' : ''}
                                ${node?.type === 'input' ? 'bg-orange-50 text-orange-700 border-orange-200' : ''}
                                ${node?.type === 'output' ? 'bg-orange-50 text-orange-700 border-orange-200' : ''}
                              `}>
                                {node?.type}
                              </Badge>
                              <span>{node?.data.label}</span>
                              {nodeId === activeNodeId && (
                                <span className="ml-auto animate-pulse">Processing...</span>
                              )}
                            </div>
                          );
                        })}
                      </div>
                    </CardContent>
                  </Card>
                </Panel>
              )}
            </ReactFlow>
          </ReactFlowProvider>
        </div>
      </div>
    </div>
  );
};

export default ComplexAgentWorkflow;