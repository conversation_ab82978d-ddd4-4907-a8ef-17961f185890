"use client";

import React from 'react';
import { <PERSON><PERSON>, <PERSON>si<PERSON> } from 'reactflow';
import { <PERSON><PERSON>, <PERSON><PERSON>, Setting<PERSON> } from 'lucide-react';

interface AgentNodeProps {
  id: string;
  data: {
    label: string;
    description?: string;
    model?: string;
    temperature?: number;
    icon?: React.ReactNode;
  };
  selected?: boolean;
}

const AgentNode: React.FC<AgentNodeProps> = ({ id, data, selected }) => {
  return (
    <div 
      className={`w-64 bg-white border-2 rounded-lg shadow-sm ${
        selected ? 'border-blue-500 ring-2 ring-blue-200' : 'border-blue-200'
      }`}
    >
      <div className="p-3 border-b border-blue-100 bg-gradient-to-r from-blue-50 to-blue-100 rounded-t-lg">
        <div className="flex items-center gap-2">
          <div className="p-2 bg-blue-100 rounded-md">
            {data.icon || <Bot className="h-4 w-4 text-blue-600" />}
          </div>
          <div>
            <h3 className="font-medium text-sm">{data.label || 'Agent'}</h3>
            <p className="text-xs text-muted-foreground">{data.model || 'gpt-4'}</p>
          </div>
          <div className="ml-auto">
            <div className="px-2 py-1 bg-blue-100 text-blue-700 text-xs rounded-full">
              Agent
            </div>
          </div>
        </div>
      </div>
      <div className="p-3">
        <p className="text-xs text-muted-foreground">{data.description || 'AI agent with configurable parameters'}</p>
        {data.temperature !== undefined && (
          <div className="mt-2">
            <div className="flex items-center justify-between text-xs">
              <span>Temperature</span>
              <span className="font-medium">{data.temperature}</span>
            </div>
            <div className="mt-1 h-1.5 w-full bg-gray-200 rounded-full overflow-hidden">
              <div 
                className="h-full bg-blue-500 rounded-full" 
                style={{ width: `${(data.temperature / 2) * 100}%` }}
              ></div>
            </div>
          </div>
        )}
      </div>
      
      {/* Handles for connections */}
      <Handle
        type="target"
        position={Position.Left}
        id="input"
        style={{ background: '#3b82f6', border: '2px solid white' }}
      />
      <Handle
        type="source"
        position={Position.Right}
        id="output"
        style={{ background: '#3b82f6', border: '2px solid white' }}
      />
    </div>
  );
};

export default AgentNode;