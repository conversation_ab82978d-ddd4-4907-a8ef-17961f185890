"use client";

import React from 'react';
import { Handle, Position } from 'reactflow';
import { MessageSquare } from 'lucide-react';

interface InputNodeProps {
  id: string;
  data: {
    label: string;
    description?: string;
    icon?: React.ReactNode;
  };
  selected?: boolean;
}

const InputNode: React.FC<InputNodeProps> = ({ id, data, selected }) => {
  return (
    <div 
      className={`w-64 bg-white border-2 rounded-lg shadow-sm ${
        selected ? 'border-orange-500 ring-2 ring-orange-200' : 'border-orange-200'
      }`}
    >
      <div className="p-3 border-b border-orange-100 bg-gradient-to-r from-orange-50 to-orange-100 rounded-t-lg">
        <div className="flex items-center gap-2">
          <div className="p-2 bg-orange-100 rounded-md">
            {data.icon || <MessageSquare className="h-4 w-4 text-orange-600" />}
          </div>
          <div>
            <h3 className="font-medium text-sm">{data.label || 'Input'}</h3>
            <p className="text-xs text-muted-foreground">Entry Point</p>
          </div>
          <div className="ml-auto">
            <div className="px-2 py-1 bg-orange-100 text-orange-700 text-xs rounded-full">
              Input
            </div>
          </div>
        </div>
      </div>
      <div className="p-3">
        <p className="text-xs text-muted-foreground">{data.description || 'Starting point for user queries'}</p>
      </div>
      
      {/* Handle for connections - only source since this is an input node */}
      <Handle
        type="source"
        position={Position.Right}
        id="output"
        style={{ background: '#f97316', border: '2px solid white' }}
      />
    </div>
  );
};

export default InputNode;