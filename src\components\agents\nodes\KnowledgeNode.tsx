"use client";

import React from 'react';
import { <PERSON><PERSON>, Position } from 'reactflow';
import { Database, Search } from 'lucide-react';

interface KnowledgeNodeProps {
  id: string;
  data: {
    label: string;
    description?: string;
    source?: string;
    similarity?: number;
    icon?: React.ReactNode;
  };
  selected?: boolean;
}

const KnowledgeNode: React.FC<KnowledgeNodeProps> = ({ id, data, selected }) => {
  return (
    <div 
      className={`w-64 bg-white border-2 rounded-lg shadow-sm ${
        selected ? 'border-green-500 ring-2 ring-green-200' : 'border-green-200'
      }`}
    >
      <div className="p-3 border-b border-green-100 bg-gradient-to-r from-green-50 to-green-100 rounded-t-lg">
        <div className="flex items-center gap-2">
          <div className="p-2 bg-green-100 rounded-md">
            {data.icon || <Database className="h-4 w-4 text-green-600" />}
          </div>
          <div>
            <h3 className="font-medium text-sm">{data.label || 'Knowledge Base'}</h3>
            <p className="text-xs text-muted-foreground">{data.source || 'Vector Database'}</p>
          </div>
          <div className="ml-auto">
            <div className="px-2 py-1 bg-green-100 text-green-700 text-xs rounded-full">
              Knowledge
            </div>
          </div>
        </div>
      </div>
      <div className="p-3">
        <p className="text-xs text-muted-foreground">{data.description || 'Vector database for information retrieval'}</p>
        
        {data.similarity !== undefined && (
          <div className="mt-2">
            <div className="flex items-center justify-between text-xs">
              <span>Similarity Threshold</span>
              <span className="font-medium">{data.similarity}</span>
            </div>
            <div className="mt-1 h-1.5 w-full bg-gray-200 rounded-full overflow-hidden">
              <div 
                className="h-full bg-green-500 rounded-full" 
                style={{ width: `${data.similarity * 100}%` }}
              ></div>
            </div>
          </div>
        )}
      </div>
      
      {/* Handles for connections */}
      <Handle
        type="target"
        position={Position.Left}
        id="input"
        style={{ background: '#10b981', border: '2px solid white' }}
      />
      <Handle
        type="source"
        position={Position.Right}
        id="output"
        style={{ background: '#10b981', border: '2px solid white' }}
      />
    </div>
  );
};

export default KnowledgeNode;