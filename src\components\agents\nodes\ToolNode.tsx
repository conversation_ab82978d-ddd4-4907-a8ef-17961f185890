"use client";

import React from 'react';
import { <PERSON><PERSON>, Position } from 'reactflow';
import { Zap, Settings } from 'lucide-react';

interface ToolNodeProps {
  id: string;
  data: {
    label: string;
    description?: string;
    toolType?: string;
    endpoint?: string;
    icon?: React.ReactNode;
  };
  selected?: boolean;
}

const ToolNode: React.FC<ToolNodeProps> = ({ id, data, selected }) => {
  return (
    <div 
      className={`w-64 bg-white border-2 rounded-lg shadow-sm ${
        selected ? 'border-purple-500 ring-2 ring-purple-200' : 'border-purple-200'
      }`}
    >
      <div className="p-3 border-b border-purple-100 bg-gradient-to-r from-purple-50 to-purple-100 rounded-t-lg">
        <div className="flex items-center gap-2">
          <div className="p-2 bg-purple-100 rounded-md">
            {data.icon || <Zap className="h-4 w-4 text-purple-600" />}
          </div>
          <div>
            <h3 className="font-medium text-sm">{data.label || 'Tool'}</h3>
            <p className="text-xs text-muted-foreground">{data.toolType || 'API'}</p>
          </div>
          <div className="ml-auto">
            <div className="px-2 py-1 bg-purple-100 text-purple-700 text-xs rounded-full">
              Tool
            </div>
          </div>
        </div>
      </div>
      <div className="p-3">
        <p className="text-xs text-muted-foreground">{data.description || 'External tool or API integration'}</p>
        
        {data.endpoint && (
          <div className="mt-2 p-2 bg-gray-50 rounded-md border border-gray-100">
            <p className="text-xs font-mono truncate" title={data.endpoint}>
              {data.endpoint}
            </p>
          </div>
        )}
      </div>
      
      {/* Handles for connections */}
      <Handle
        type="target"
        position={Position.Left}
        id="input"
        style={{ background: '#8b5cf6', border: '2px solid white' }}
      />
      <Handle
        type="source"
        position={Position.Right}
        id="output"
        style={{ background: '#8b5cf6', border: '2px solid white' }}
      />
    </div>
  );
};

export default ToolNode;