// APIX WebSocket Server Implementation
// Production-ready WebSocket server with connection pooling, authentication,
// subscription management, and cross-module event routing

import { WebSocketServer, WebSocket } from "ws";
import { IncomingMessage } from "http";
import { URL } from "url";
import { APXEvent, APXSubscription } from "./apix-client";
import { verifyToken, JWTPayload } from "./auth-context";

interface ServerConnection {
  id: string;
  ws: WebSocket;
  organizationId: string;
  userId: string;
  subscriptions: Set<string>;
  lastHeartbeat: number;
  authenticated: boolean;
  metadata: {
    userAgent?: string;
    ipAddress?: string;
    capabilities: string[];
  };
}

interface ServerMetrics {
  totalConnections: number;
  activeConnections: number;
  totalEvents: number;
  eventsPerSecond: number;
  avgLatency: number;
  errorRate: number;
  memoryUsage: number;
}

class APXServer {
  private wss: WebSocketServer;
  private connections: Map<string, ServerConnection> = new Map();
  private subscriptions: Map<string, Set<string>> = new Map(); // subscription -> connection IDs
  private eventStore: Map<string, APXEvent[]> = new Map(); // For event replay
  private rateLimiter: Map<string, { count: number; resetTime: number }> =
    new Map();
  private metrics: ServerMetrics = {
    totalConnections: 0,
    activeConnections: 0,
    totalEvents: 0,
    eventsPerSecond: 0,
    avgLatency: 0,
    errorRate: 0,
    memoryUsage: 0,
  };
  private heartbeatInterval: NodeJS.Timeout;
  private metricsInterval: NodeJS.Timeout;
  private cleanupInterval: NodeJS.Timeout;

  constructor(port: number = 8080) {
    this.wss = new WebSocketServer({
      port,
      perMessageDeflate: true, // Enable compression
      maxPayload: 1024 * 1024, // 1MB max message size
      clientTracking: true,
      verifyClient: this.verifyClient.bind(this),
    });

    this.setupEventHandlers();
    this.startHeartbeat();
    this.startMetricsCollection();
    this.startCleanupProcess();

    console.log(`APIX WebSocket Server started on port ${port}`);
  }

  private async verifyClient(info: {
    origin: string;
    secure: boolean;
    req: IncomingMessage;
  }): Promise<boolean> {
    try {
      const url = new URL(info.req.url!, `http://${info.req.headers.host}`);
      const token = url.searchParams.get("token");
      const org = url.searchParams.get("org");

      if (!token || !org) {
        console.warn("Missing token or organization in WebSocket connection");
        return false;
      }

      // Verify JWT token
      const payload = await verifyToken(token);
      if (!payload || payload.org !== org) {
        console.warn("Invalid token or organization mismatch");
        return false;
      }

      return true;
    } catch (error) {
      console.error("Client verification failed:", error);
      return false;
    }
  }

  private setupEventHandlers(): void {
    this.wss.on("connection", this.handleConnection.bind(this));
    this.wss.on("error", this.handleServerError.bind(this));

    // Graceful shutdown
    process.on("SIGTERM", this.shutdown.bind(this));
    process.on("SIGINT", this.shutdown.bind(this));
  }

  private async handleConnection(
    ws: WebSocket,
    req: IncomingMessage,
  ): Promise<void> {
    const url = new URL(req.url!, `http://${req.headers.host}`);
    const connectionId = url.searchParams.get("conn") || `conn_${Date.now()}`;
    const organizationId = url.searchParams.get("org")!;
    const token = url.searchParams.get("token")!;
    const capabilities = JSON.parse(
      url.searchParams.get("capabilities") || "[]",
    );

    try {
      // Verify token again for security
      const payload = await verifyToken(token);
      if (!payload) {
        ws.close(1008, "Invalid authentication");
        return;
      }

      const connection: ServerConnection = {
        id: connectionId,
        ws,
        organizationId,
        userId: payload.sub,
        subscriptions: new Set(),
        lastHeartbeat: Date.now(),
        authenticated: true,
        metadata: {
          userAgent: req.headers["user-agent"],
          ipAddress: req.socket.remoteAddress,
          capabilities,
        },
      };

      this.connections.set(connectionId, connection);
      this.metrics.totalConnections++;
      this.metrics.activeConnections++;

      console.log(
        `Client connected: ${connectionId} (${organizationId}/${payload.sub})`,
      );

      // Setup connection event handlers
      ws.on("message", (data) => this.handleMessage(connectionId, data));
      ws.on("close", () => this.handleDisconnection(connectionId));
      ws.on("error", (error) =>
        this.handleConnectionError(connectionId, error),
      );
      ws.on("pong", () => this.handlePong(connectionId));

      // Send connection acknowledgment
      this.sendToConnection(connectionId, {
        id: `ack_${connectionId}`,
        type: "connection_acknowledged",
        module: "system",
        organizationId,
        userId: payload.sub,
        timestamp: new Date().toISOString(),
        data: {
          connectionId,
          serverVersion: "2.0.0",
          capabilities: ["compression", "batching", "replay", "pubsub"],
          limits: {
            maxSubscriptions: 100,
            maxEventsPerSecond: 1000,
            maxMessageSize: 1024 * 1024,
          },
        },
        version: 1,
      });
    } catch (error) {
      console.error("Error handling connection:", error);
      ws.close(1011, "Server error");
    }
  }

  private handleMessage(connectionId: string, data: Buffer | string): void {
    const connection = this.connections.get(connectionId);
    if (!connection) return;

    try {
      let message: any;

      // Handle compressed messages
      if (typeof data === "string" && data.startsWith("compressed:")) {
        message = JSON.parse(this.decompress(data.substring(11)));
      } else {
        message = JSON.parse(data.toString());
      }

      // Rate limiting
      if (!this.checkRateLimit(connectionId)) {
        this.sendError(connectionId, "Rate limit exceeded");
        return;
      }

      // Handle different message types
      switch (message.type) {
        case "subscribe":
          this.handleSubscription(connectionId, message);
          break;
        case "unsubscribe":
          this.handleUnsubscription(connectionId, message);
          break;
        case "event":
          this.handleEventBroadcast(connectionId, message);
          break;
        case "heartbeat":
          this.handleHeartbeat(connectionId, message);
          break;
        case "event_replay_request":
          this.handleEventReplay(connectionId, message);
          break;
        case "workflow_state_recovery_request":
          this.handleWorkflowStateRecovery(connectionId, message);
          break;
        default:
          console.warn(`Unknown message type: ${message.type}`);
      }

      this.metrics.totalEvents++;
    } catch (error) {
      console.error(`Error processing message from ${connectionId}:`, error);
      this.sendError(connectionId, "Invalid message format");
    }
  }

  private handleSubscription(connectionId: string, message: any): void {
    const connection = this.connections.get(connectionId);
    if (!connection) return;

    const subscriptionKey = `${message.data.module}:${message.data.eventTypes.join(",")}`;

    // Add to connection subscriptions
    connection.subscriptions.add(subscriptionKey);

    // Add to global subscriptions map
    if (!this.subscriptions.has(subscriptionKey)) {
      this.subscriptions.set(subscriptionKey, new Set());
    }
    this.subscriptions.get(subscriptionKey)!.add(connectionId);

    console.log(`Subscription added: ${connectionId} -> ${subscriptionKey}`);

    // Send acknowledgment
    this.sendToConnection(connectionId, {
      id: `sub_ack_${Date.now()}`,
      type: "subscription_acknowledged",
      module: "system",
      organizationId: connection.organizationId,
      userId: connection.userId,
      timestamp: new Date().toISOString(),
      data: {
        subscriptionId: message.data.subscriptionId,
        subscriptionKey,
      },
      version: 1,
    });
  }

  private handleUnsubscription(connectionId: string, message: any): void {
    const connection = this.connections.get(connectionId);
    if (!connection) return;

    const subscriptionKey = message.data.subscriptionKey;

    // Remove from connection subscriptions
    connection.subscriptions.delete(subscriptionKey);

    // Remove from global subscriptions map
    const subscribers = this.subscriptions.get(subscriptionKey);
    if (subscribers) {
      subscribers.delete(connectionId);
      if (subscribers.size === 0) {
        this.subscriptions.delete(subscriptionKey);
      }
    }

    console.log(`Subscription removed: ${connectionId} -> ${subscriptionKey}`);
  }

  private handleEventBroadcast(connectionId: string, message: any): void {
    const connection = this.connections.get(connectionId);
    if (!connection) return;

    const event: APXEvent = message.data;

    // Validate event
    if (!this.validateEvent(event, connection)) {
      this.sendError(connectionId, "Invalid event data");
      return;
    }

    // Store event for replay
    this.storeEvent(event);

    // Broadcast to subscribers
    this.broadcastEvent(event);

    // Cross-module routing
    this.routeCrossModuleEvent(event);
  }

  private validateEvent(
    event: APXEvent,
    connection: ServerConnection,
  ): boolean {
    // Ensure organization isolation
    if (event.organizationId !== connection.organizationId) {
      return false;
    }

    // Validate required fields
    if (!event.id || !event.type || !event.module || !event.timestamp) {
      return false;
    }

    return true;
  }

  private storeEvent(event: APXEvent): void {
    const key = `${event.organizationId}:${event.module}`;
    if (!this.eventStore.has(key)) {
      this.eventStore.set(key, []);
    }

    const events = this.eventStore.get(key)!;
    events.push(event);

    // Keep only last 10000 events per organization/module
    if (events.length > 10000) {
      events.splice(0, events.length - 10000);
    }
  }

  private broadcastEvent(event: APXEvent): void {
    const subscriptionKey = `${event.module}:${event.type}`;
    const wildcardKey = `${event.module}:*`;
    const globalKey = `*:${event.type}`;

    const subscribers = new Set<string>();

    // Add specific subscribers
    this.subscriptions
      .get(subscriptionKey)
      ?.forEach((id) => subscribers.add(id));
    // Add wildcard subscribers
    this.subscriptions.get(wildcardKey)?.forEach((id) => subscribers.add(id));
    // Add global subscribers
    this.subscriptions.get(globalKey)?.forEach((id) => subscribers.add(id));

    subscribers.forEach((connectionId) => {
      const connection = this.connections.get(connectionId);
      if (connection && connection.organizationId === event.organizationId) {
        this.sendToConnection(connectionId, event);
      }
    });
  }

  private routeCrossModuleEvent(event: APXEvent): void {
    // Implement cross-module routing logic
    // This mirrors the client-side routing but happens server-side for efficiency

    if (event.module === "agents" && event.type === "execution_completed") {
      // Route to tools module
      const crossEvent: APXEvent = {
        id: `cross_${Date.now()}`,
        type: "agent_result_available",
        module: "tools",
        organizationId: event.organizationId,
        userId: event.userId,
        timestamp: new Date().toISOString(),
        data: {
          sourceModule: "agents",
          agentId: event.data.agentId,
          result: event.data.output,
        },
        version: 1,
        correlationId: event.id,
      };
      this.broadcastEvent(crossEvent);
    }
  }

  private handleEventReplay(connectionId: string, message: any): void {
    const connection = this.connections.get(connectionId);
    if (!connection) return;

    const {
      fromTimestamp,
      toTimestamp,
      eventTypes,
      modules,
      maxEvents = 1000,
    } = message.data;
    const key = `${connection.organizationId}:${modules?.[0] || "*"}`;
    const events = this.eventStore.get(key) || [];

    let filteredEvents = events;

    // Apply filters
    if (fromTimestamp) {
      filteredEvents = filteredEvents.filter(
        (e) => e.timestamp >= fromTimestamp,
      );
    }
    if (toTimestamp) {
      filteredEvents = filteredEvents.filter((e) => e.timestamp <= toTimestamp);
    }
    if (eventTypes?.length) {
      filteredEvents = filteredEvents.filter((e) =>
        eventTypes.includes(e.type),
      );
    }
    if (modules?.length) {
      filteredEvents = filteredEvents.filter((e) => modules.includes(e.module));
    }

    // Limit results
    filteredEvents = filteredEvents.slice(-maxEvents);

    // Send replay response
    this.sendToConnection(connectionId, {
      id: `replay_response_${Date.now()}`,
      type: "event_replay_response",
      module: "system",
      organizationId: connection.organizationId,
      userId: connection.userId,
      timestamp: new Date().toISOString(),
      data: {
        requestId: message.id,
        events: filteredEvents,
        totalCount: filteredEvents.length,
      },
      version: 1,
    });
  }

  private handleWorkflowStateRecovery(
    connectionId: string,
    message: any,
  ): void {
    const connection = this.connections.get(connectionId);
    if (!connection) return;

    // In a real implementation, this would query a database
    // For now, we'll simulate workflow state recovery
    const workflowId = message.data.workflowId;
    const mockState = {
      workflowId,
      currentStep: "step_2",
      variables: { user_input: "Hello", processed: true },
      executionHistory: ["step_1_completed", "step_2_started"],
      lastUpdated: new Date().toISOString(),
    };

    this.sendToConnection(connectionId, {
      id: `recovery_response_${Date.now()}`,
      type: "workflow_state_recovery_response",
      module: "system",
      organizationId: connection.organizationId,
      userId: connection.userId,
      timestamp: new Date().toISOString(),
      data: {
        requestId: message.id,
        state: mockState,
      },
      version: 1,
    });
  }

  private handleHeartbeat(connectionId: string, message: any): void {
    const connection = this.connections.get(connectionId);
    if (!connection) return;

    connection.lastHeartbeat = Date.now();

    // Send heartbeat response
    this.sendToConnection(connectionId, {
      id: `heartbeat_response_${Date.now()}`,
      type: "heartbeat",
      module: "system",
      organizationId: connection.organizationId,
      userId: connection.userId,
      timestamp: new Date().toISOString(),
      data: {
        serverTime: new Date().toISOString(),
        connectionId,
      },
      version: 1,
    });
  }

  private handlePong(connectionId: string): void {
    const connection = this.connections.get(connectionId);
    if (connection) {
      connection.lastHeartbeat = Date.now();
    }
  }

  private handleDisconnection(connectionId: string): void {
    const connection = this.connections.get(connectionId);
    if (!connection) return;

    // Remove from all subscriptions
    connection.subscriptions.forEach((subscriptionKey) => {
      const subscribers = this.subscriptions.get(subscriptionKey);
      if (subscribers) {
        subscribers.delete(connectionId);
        if (subscribers.size === 0) {
          this.subscriptions.delete(subscriptionKey);
        }
      }
    });

    this.connections.delete(connectionId);
    this.metrics.activeConnections--;

    console.log(`Client disconnected: ${connectionId}`);
  }

  private handleConnectionError(connectionId: string, error: Error): void {
    console.error(`Connection error for ${connectionId}:`, error);
    this.metrics.errorRate++;
  }

  private handleServerError(error: Error): void {
    console.error("WebSocket server error:", error);
  }

  private sendToConnection(connectionId: string, data: any): void {
    const connection = this.connections.get(connectionId);
    if (!connection || connection.ws.readyState !== WebSocket.OPEN) {
      return;
    }

    try {
      let payload = JSON.stringify(data);

      // Apply compression for large messages
      if (payload.length > 1024) {
        payload = "compressed:" + this.compress(payload);
      }

      connection.ws.send(payload);
    } catch (error) {
      console.error(`Error sending to ${connectionId}:`, error);
    }
  }

  private sendError(connectionId: string, message: string): void {
    this.sendToConnection(connectionId, {
      id: `error_${Date.now()}`,
      type: "error",
      module: "system",
      organizationId: "",
      userId: "",
      timestamp: new Date().toISOString(),
      data: { error: message },
      version: 1,
    });
  }

  private checkRateLimit(connectionId: string): boolean {
    const now = Date.now();
    const windowStart = Math.floor(now / 1000) * 1000;
    const key = `${connectionId}_${windowStart}`;

    const current = this.rateLimiter.get(key) || {
      count: 0,
      resetTime: windowStart,
    };

    if (current.count >= 1000) {
      // 1000 events per second limit
      return false;
    }

    this.rateLimiter.set(key, {
      count: current.count + 1,
      resetTime: current.resetTime,
    });

    return true;
  }

  private startHeartbeat(): void {
    this.heartbeatInterval = setInterval(() => {
      const now = Date.now();
      const staleConnections: string[] = [];

      this.connections.forEach((connection, connectionId) => {
        if (now - connection.lastHeartbeat > 60000) {
          // 60 seconds timeout
          staleConnections.push(connectionId);
        } else {
          // Send ping
          if (connection.ws.readyState === WebSocket.OPEN) {
            connection.ws.ping();
          }
        }
      });

      // Clean up stale connections
      staleConnections.forEach((connectionId) => {
        const connection = this.connections.get(connectionId);
        if (connection) {
          connection.ws.terminate();
          this.handleDisconnection(connectionId);
        }
      });
    }, 30000); // Every 30 seconds
  }

  private startMetricsCollection(): void {
    this.metricsInterval = setInterval(() => {
      this.metrics.memoryUsage = process.memoryUsage().heapUsed;

      // Calculate events per second
      const now = Date.now();
      // This is a simplified calculation - in production you'd want a proper sliding window
      this.metrics.eventsPerSecond =
        this.metrics.totalEvents / ((now - Date.now()) / 1000 || 1);

      console.log("APIX Server Metrics:", this.metrics);
    }, 60000); // Every minute
  }

  private startCleanupProcess(): void {
    this.cleanupInterval = setInterval(() => {
      // Clean up rate limiter
      const now = Date.now();
      const cutoff = now - 2000;

      for (const [key, value] of Array.from(this.rateLimiter.entries())) {
        if (value.resetTime < cutoff) {
          this.rateLimiter.delete(key);
        }
      }

      // Clean up old events (keep only last 24 hours)
      const dayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString();
      this.eventStore.forEach((events, key) => {
        const filteredEvents = events.filter((e) => e.timestamp > dayAgo);
        this.eventStore.set(key, filteredEvents);
      });
    }, 300000); // Every 5 minutes
  }

  private compress(data: string): string {
    // Simple compression simulation - in production use actual compression
    return Buffer.from(data).toString("base64");
  }

  private decompress(data: string): string {
    // Simple decompression simulation - in production use actual decompression
    return Buffer.from(data, "base64").toString();
  }

  public getMetrics(): ServerMetrics {
    return { ...this.metrics };
  }

  public getConnectionCount(): number {
    return this.connections.size;
  }

  public getSubscriptionCount(): number {
    return this.subscriptions.size;
  }

  private shutdown(): void {
    console.log("Shutting down APIX server...");

    // Clear intervals
    if (this.heartbeatInterval) clearInterval(this.heartbeatInterval);
    if (this.metricsInterval) clearInterval(this.metricsInterval);
    if (this.cleanupInterval) clearInterval(this.cleanupInterval);

    // Close all connections
    this.connections.forEach((connection) => {
      connection.ws.close(1001, "Server shutdown");
    });

    // Close server
    this.wss.close(() => {
      console.log("APIX server shut down complete");
      process.exit(0);
    });
  }
}

// Export for use in server applications
export { APXServer, ServerConnection, ServerMetrics };

// Auto-start server if this file is run directly
if (require.main === module) {
  const port = parseInt(process.env.APIX_PORT || "8080");
  new APXServer(port);
}
