// APIX Real-time State Synchronization with Zustand Integration
// Provides real-time state synchronization across all platform modules

import { create } from "zustand";
import { subscribeWithSelector } from "zustand/middleware";
import { getAPXClient, APXEvent } from "./apix-client";
import { mockAuthContext } from "./auth-context";

// State interfaces for different modules
interface AgentState {
  agents: Record<string, any>;
  activeExecutions: Record<string, any>;
  templates: Record<string, any>;
}

interface ToolState {
  tools: Record<string, any>;
  executions: Record<string, any>;
  marketplace: Record<string, any>;
}

interface HybridState {
  workflows: Record<string, any>;
  executions: Record<string, any>;
  templates: Record<string, any>;
}

interface WidgetState {
  widgets: Record<string, any>;
  embeddings: Record<string, any>;
  analytics: Record<string, any>;
}

interface SandboxState {
  sandboxes: Record<string, any>;
  tests: Record<string, any>;
  results: Record<string, any>;
}

interface AnalyticsState {
  metrics: Record<string, any>;
  dashboards: Record<string, any>;
  reports: Record<string, any>;
}

interface GlobalState {
  agents: AgentState;
  tools: ToolState;
  hybrids: HybridState;
  widgets: WidgetState;
  sandbox: SandboxState;
  analytics: AnalyticsState;
  connectionStatus: "connecting" | "connected" | "disconnected" | "error";
  lastSync: string;
  syncErrors: string[];
}

// Zustand store with real-time synchronization
const useAPXStore = create<
  GlobalState & {
    // Actions
    updateAgentState: (agentId: string, data: any) => void;
    updateToolState: (toolId: string, data: any) => void;
    updateWorkflowState: (workflowId: string, data: any) => void;
    updateWidgetState: (widgetId: string, data: any) => void;
    updateSandboxState: (sandboxId: string, data: any) => void;
    updateAnalyticsState: (metricId: string, data: any) => void;
    setConnectionStatus: (status: GlobalState["connectionStatus"]) => void;
    addSyncError: (error: string) => void;
    clearSyncErrors: () => void;
    syncFromServer: () => Promise<void>;
    broadcastStateChange: (module: string, id: string, data: any) => void;
  }
>(
  subscribeWithSelector((set, get) => ({
    // Initial state
    agents: {
      agents: {},
      activeExecutions: {},
      templates: {},
    },
    tools: {
      tools: {},
      executions: {},
      marketplace: {},
    },
    hybrids: {
      workflows: {},
      executions: {},
      templates: {},
    },
    widgets: {
      widgets: {},
      embeddings: {},
      analytics: {},
    },
    sandbox: {
      sandboxes: {},
      tests: {},
      results: {},
    },
    analytics: {
      metrics: {},
      dashboards: {},
      reports: {},
    },
    connectionStatus: "disconnected",
    lastSync: new Date().toISOString(),
    syncErrors: [],

    // Actions
    updateAgentState: (agentId: string, data: any) => {
      set((state) => ({
        agents: {
          ...state.agents,
          agents: {
            ...state.agents.agents,
            [agentId]: { ...state.agents.agents[agentId], ...data },
          },
        },
        lastSync: new Date().toISOString(),
      }));
      get().broadcastStateChange("agents", agentId, data);
    },

    updateToolState: (toolId: string, data: any) => {
      set((state) => ({
        tools: {
          ...state.tools,
          tools: {
            ...state.tools.tools,
            [toolId]: { ...state.tools.tools[toolId], ...data },
          },
        },
        lastSync: new Date().toISOString(),
      }));
      get().broadcastStateChange("tools", toolId, data);
    },

    updateWorkflowState: (workflowId: string, data: any) => {
      set((state) => ({
        hybrids: {
          ...state.hybrids,
          workflows: {
            ...state.hybrids.workflows,
            [workflowId]: { ...state.hybrids.workflows[workflowId], ...data },
          },
        },
        lastSync: new Date().toISOString(),
      }));
      get().broadcastStateChange("hybrids", workflowId, data);
    },

    updateWidgetState: (widgetId: string, data: any) => {
      set((state) => ({
        widgets: {
          ...state.widgets,
          widgets: {
            ...state.widgets.widgets,
            [widgetId]: { ...state.widgets.widgets[widgetId], ...data },
          },
        },
        lastSync: new Date().toISOString(),
      }));
      get().broadcastStateChange("widgets", widgetId, data);
    },

    updateSandboxState: (sandboxId: string, data: any) => {
      set((state) => ({
        sandbox: {
          ...state.sandbox,
          sandboxes: {
            ...state.sandbox.sandboxes,
            [sandboxId]: { ...state.sandbox.sandboxes[sandboxId], ...data },
          },
        },
        lastSync: new Date().toISOString(),
      }));
      get().broadcastStateChange("sandbox", sandboxId, data);
    },

    updateAnalyticsState: (metricId: string, data: any) => {
      set((state) => ({
        analytics: {
          ...state.analytics,
          metrics: {
            ...state.analytics.metrics,
            [metricId]: { ...state.analytics.metrics[metricId], ...data },
          },
        },
        lastSync: new Date().toISOString(),
      }));
      get().broadcastStateChange("analytics", metricId, data);
    },

    setConnectionStatus: (status: GlobalState["connectionStatus"]) => {
      set({ connectionStatus: status });
    },

    addSyncError: (error: string) => {
      set((state) => ({
        syncErrors: [...state.syncErrors, error].slice(-10), // Keep last 10 errors
      }));
    },

    clearSyncErrors: () => {
      set({ syncErrors: [] });
    },

    syncFromServer: async () => {
      try {
        const { user, organization } = mockAuthContext;
        if (!user || !organization) return;

        const apixClient = getAPXClient(
          organization.id,
          user.id,
          mockAuthContext.token!,
        );

        // Request full state sync
        apixClient.sendEvent({
          id: `state_sync_request_${Date.now()}`,
          type: "full_state_sync_request",
          module: "system",
          organizationId: organization.id,
          userId: user.id,
          timestamp: new Date().toISOString(),
          data: {
            modules: [
              "agents",
              "tools",
              "hybrids",
              "widgets",
              "sandbox",
              "analytics",
            ],
            lastSync: get().lastSync,
          },
          version: 1,
        });
      } catch (error) {
        get().addSyncError(
          `Sync failed: ${error instanceof Error ? error.message : "Unknown error"}`,
        );
      }
    },

    broadcastStateChange: (module: string, id: string, data: any) => {
      try {
        const { user, organization } = mockAuthContext;
        if (!user || !organization) return;

        const apixClient = getAPXClient(
          organization.id,
          user.id,
          mockAuthContext.token!,
        );

        apixClient.sendEvent({
          id: `state_change_${Date.now()}`,
          type: "state_changed",
          module,
          organizationId: organization.id,
          userId: user.id,
          timestamp: new Date().toISOString(),
          data: {
            entityId: id,
            changes: data,
            source: "client",
          },
          version: 1,
        });
      } catch (error) {
        get().addSyncError(
          `Broadcast failed: ${error instanceof Error ? error.message : "Unknown error"}`,
        );
      }
    },
  })),
);

// Real-time synchronization manager
class APXStateSyncManager {
  private static instance: APXStateSyncManager;
  private apixClient: any = null;
  private subscriptions: string[] = [];
  private syncInterval: NodeJS.Timeout | null = null;

  static getInstance(): APXStateSyncManager {
    if (!APXStateSyncManager.instance) {
      APXStateSyncManager.instance = new APXStateSyncManager();
    }
    return APXStateSyncManager.instance;
  }

  async initialize(): Promise<void> {
    try {
      const { user, organization } = mockAuthContext;
      if (!user || !organization) {
        throw new Error("Authentication required for state sync");
      }

      useAPXStore.getState().setConnectionStatus("connecting");

      this.apixClient = getAPXClient(
        organization.id,
        user.id,
        mockAuthContext.token!,
      );
      await this.apixClient.connect();

      useAPXStore.getState().setConnectionStatus("connected");

      // Subscribe to all state change events
      this.setupSubscriptions();

      // Start periodic sync
      this.startPeriodicSync();

      console.log("APIX State Sync Manager initialized");
    } catch (error) {
      useAPXStore.getState().setConnectionStatus("error");
      useAPXStore
        .getState()
        .addSyncError(
          `Initialization failed: ${error instanceof Error ? error.message : "Unknown error"}`,
        );
      throw error;
    }
  }

  private setupSubscriptions(): void {
    if (!this.apixClient) return;

    // Subscribe to state changes from other clients
    const stateChangeSubscription = this.apixClient.subscribe(
      "*",
      ["state_changed", "state_sync_response"],
      this.handleStateChangeEvent.bind(this),
    );
    this.subscriptions.push(stateChangeSubscription);

    // Subscribe to module-specific events
    const modules = [
      "agents",
      "tools",
      "hybrids",
      "widgets",
      "sandbox",
      "analytics",
    ];
    modules.forEach((module) => {
      const subscription = this.apixClient.subscribe(
        module,
        ["created", "updated", "deleted", "executed"],
        this.handleModuleEvent.bind(this),
      );
      this.subscriptions.push(subscription);
    });

    // Subscribe to system events
    const systemSubscription = this.apixClient.subscribe(
      "system",
      ["full_state_sync_response"],
      this.handleSystemEvent.bind(this),
    );
    this.subscriptions.push(systemSubscription);
  }

  private handleStateChangeEvent(event: APXEvent): void {
    try {
      if (event.type === "state_changed" && event.data.source !== "client") {
        // Apply remote state changes locally
        const { module, data } = event;
        const { entityId, changes } = event.data;

        switch (module) {
          case "agents":
            useAPXStore.getState().updateAgentState(entityId, changes);
            break;
          case "tools":
            useAPXStore.getState().updateToolState(entityId, changes);
            break;
          case "hybrids":
            useAPXStore.getState().updateWorkflowState(entityId, changes);
            break;
          case "widgets":
            useAPXStore.getState().updateWidgetState(entityId, changes);
            break;
          case "sandbox":
            useAPXStore.getState().updateSandboxState(entityId, changes);
            break;
          case "analytics":
            useAPXStore.getState().updateAnalyticsState(entityId, changes);
            break;
        }
      }
    } catch (error) {
      useAPXStore
        .getState()
        .addSyncError(
          `State change handling failed: ${error instanceof Error ? error.message : "Unknown error"}`,
        );
    }
  }

  private handleModuleEvent(event: APXEvent): void {
    try {
      const { module, type, data } = event;
      const entityId =
        data.id ||
        data.agentId ||
        data.toolId ||
        data.workflowId ||
        data.widgetId ||
        data.sandboxId;

      if (!entityId) return;

      switch (type) {
        case "created":
        case "updated":
          switch (module) {
            case "agents":
              useAPXStore.getState().updateAgentState(entityId, data);
              break;
            case "tools":
              useAPXStore.getState().updateToolState(entityId, data);
              break;
            case "hybrids":
              useAPXStore.getState().updateWorkflowState(entityId, data);
              break;
            case "widgets":
              useAPXStore.getState().updateWidgetState(entityId, data);
              break;
            case "sandbox":
              useAPXStore.getState().updateSandboxState(entityId, data);
              break;
            case "analytics":
              useAPXStore.getState().updateAnalyticsState(entityId, data);
              break;
          }
          break;
        case "deleted":
          // Handle deletions by removing from state
          // Implementation would depend on specific requirements
          break;
      }
    } catch (error) {
      useAPXStore
        .getState()
        .addSyncError(
          `Module event handling failed: ${error instanceof Error ? error.message : "Unknown error"}`,
        );
    }
  }

  private handleSystemEvent(event: APXEvent): void {
    try {
      if (event.type === "full_state_sync_response") {
        // Apply full state sync from server
        const { states } = event.data;

        Object.entries(states).forEach(
          ([module, moduleState]: [string, any]) => {
            switch (module) {
              case "agents":
                Object.entries(moduleState).forEach(([id, data]) => {
                  useAPXStore.getState().updateAgentState(id, data);
                });
                break;
              case "tools":
                Object.entries(moduleState).forEach(([id, data]) => {
                  useAPXStore.getState().updateToolState(id, data);
                });
                break;
              case "hybrids":
                Object.entries(moduleState).forEach(([id, data]) => {
                  useAPXStore.getState().updateWorkflowState(id, data);
                });
                break;
              case "widgets":
                Object.entries(moduleState).forEach(([id, data]) => {
                  useAPXStore.getState().updateWidgetState(id, data);
                });
                break;
              case "sandbox":
                Object.entries(moduleState).forEach(([id, data]) => {
                  useAPXStore.getState().updateSandboxState(id, data);
                });
                break;
              case "analytics":
                Object.entries(moduleState).forEach(([id, data]) => {
                  useAPXStore.getState().updateAnalyticsState(id, data);
                });
                break;
            }
          },
        );
      }
    } catch (error) {
      useAPXStore
        .getState()
        .addSyncError(
          `System event handling failed: ${error instanceof Error ? error.message : "Unknown error"}`,
        );
    }
  }

  private startPeriodicSync(): void {
    // Sync every 30 seconds
    this.syncInterval = setInterval(() => {
      useAPXStore.getState().syncFromServer();
    }, 30000);
  }

  disconnect(): void {
    if (this.syncInterval) {
      clearInterval(this.syncInterval);
      this.syncInterval = null;
    }

    this.subscriptions.forEach((subscriptionId) => {
      if (this.apixClient) {
        this.apixClient.unsubscribe(subscriptionId);
      }
    });
    this.subscriptions = [];

    if (this.apixClient) {
      this.apixClient.disconnect();
      this.apixClient = null;
    }

    useAPXStore.getState().setConnectionStatus("disconnected");
  }
}

// Hooks for easy component integration
export const useAgentState = () => useAPXStore((state) => state.agents);
export const useToolState = () => useAPXStore((state) => state.tools);
export const useWorkflowState = () => useAPXStore((state) => state.hybrids);
export const useWidgetState = () => useAPXStore((state) => state.widgets);
export const useSandboxState = () => useAPXStore((state) => state.sandbox);
export const useAnalyticsState = () => useAPXStore((state) => state.analytics);
export const useConnectionStatus = () =>
  useAPXStore((state) => state.connectionStatus);
export const useSyncErrors = () => useAPXStore((state) => state.syncErrors);

// Actions
export const useAPXActions = () => ({
  updateAgentState: useAPXStore((state) => state.updateAgentState),
  updateToolState: useAPXStore((state) => state.updateToolState),
  updateWorkflowState: useAPXStore((state) => state.updateWorkflowState),
  updateWidgetState: useAPXStore((state) => state.updateWidgetState),
  updateSandboxState: useAPXStore((state) => state.updateSandboxState),
  updateAnalyticsState: useAPXStore((state) => state.updateAnalyticsState),
  syncFromServer: useAPXStore((state) => state.syncFromServer),
  clearSyncErrors: useAPXStore((state) => state.clearSyncErrors),
});

// Export the store and manager
export { useAPXStore, APXStateSyncManager };

// Auto-initialize if in browser environment
if (typeof window !== "undefined") {
  const syncManager = APXStateSyncManager.getInstance();

  // Initialize when auth context is available
  if (mockAuthContext.user && mockAuthContext.organization) {
    syncManager.initialize().catch(console.error);
  }

  // Cleanup on page unload
  window.addEventListener("beforeunload", () => {
    syncManager.disconnect();
  });
}
