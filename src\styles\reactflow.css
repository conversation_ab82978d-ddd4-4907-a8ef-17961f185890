/* React Flow Styles */
.react-flow {
  width: 100%;
  height: 100%;
}

.react-flow__node {
  cursor: pointer;
}

.react-flow__node.selected {
  box-shadow: 0 0 0 2px #3b82f6;
}

.react-flow__edge {
  cursor: pointer;
}

.react-flow__edge.selected .react-flow__edge-path {
  stroke: #3b82f6;
  stroke-width: 3;
}

.react-flow__handle {
  width: 12px;
  height: 12px;
  border: 2px solid white;
  border-radius: 50%;
  cursor: crosshair;
}

.react-flow__handle-connecting {
  background: #3b82f6;
}

.react-flow__handle-valid {
  background: #10b981;
}

.react-flow__handle-invalid {
  background: #ef4444;
}

.react-flow__connection-line {
  stroke: #3b82f6;
  stroke-width: 2;
  stroke-dasharray: 5;
}

.react-flow__controls {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  background: white;
  border: 1px solid #e5e7eb;
}

.react-flow__controls button {
  border: none;
  background: transparent;
  padding: 8px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.react-flow__controls button:hover {
  background: #f3f4f6;
}

.react-flow__minimap {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.react-flow__background {
  background-color: #f9fafb;
}

.react-flow__background.dots {
  background-image: radial-gradient(circle, #d1d5db 1px, transparent 1px);
  background-size: 20px 20px;
}

.react-flow__background.lines {
  background-image: linear-gradient(to right, #e5e7eb 1px, transparent 1px),
    linear-gradient(to bottom, #e5e7eb 1px, transparent 1px);
  background-size: 20px 20px;
}

.react-flow__panel {
  z-index: 10;
}

/* Custom node animations */
@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Custom edge label styles */
.react-flow__edge-label {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 4px;
  padding: 2px 6px;
  font-size: 12px;
  color: #374151;
}

/* Node status indicators */
.node-status-idle {
  border-color: #d1d5db;
}

.node-status-running {
  border-color: #3b82f6;
  animation: pulse 2s infinite;
}

.node-status-completed {
  border-color: #10b981;
}

.node-status-failed {
  border-color: #ef4444;
}

.node-status-paused {
  border-color: #f59e0b;
}

/* Connection validation styles */
.react-flow__handle.connectable {
  cursor: crosshair;
}

.react-flow__handle.connectable:hover {
  transform: scale(1.2);
  transition: transform 0.2s;
}

/* Enhanced scrollbar */
.react-flow::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.react-flow::-webkit-scrollbar-track {
  background: #f8fafc;
  border-radius: 4px;
}

.react-flow::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #cbd5e1, #94a3b8);
  border-radius: 4px;
  transition: all 0.2s ease;
}

.react-flow::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #94a3b8, #64748b);
}

/* Drag and drop styles */
.react-flow__node.dragging {
  opacity: 0.8;
  transform: rotate(2deg);
}

.react-flow__edge.animated {
  stroke-dasharray: 5;
  animation: dash 1s linear infinite;
}

/* Selection styles */
.react-flow__node.selected {
  box-shadow:
    0 0 0 3px rgba(59, 130, 246, 0.3),
    0 8px 25px -5px rgba(0, 0, 0, 0.15);
}

.react-flow__edge.selected .react-flow__edge-path {
  stroke: #3b82f6;
  stroke-width: 4;
  filter: drop-shadow(0 0 6px rgba(59, 130, 246, 0.4));
}

/* Panel styles */
.react-flow__panel {
  z-index: 10;
}

.react-flow__panel.top-right {
  top: 10px;
  right: 10px;
}

/* Node palette drag preview */
.node-palette-item {
  transition: all 0.2s ease;
}

.node-palette-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.node-palette-item.dragging {
  opacity: 0.5;
  transform: rotate(5deg) scale(0.95);
}

/* Canvas grid enhancement */
.react-flow__background.dots {
  opacity: 0.4;
}

.react-flow__background.lines {
  opacity: 0.3;
}

/* Workflow execution animation */
@keyframes flow {
  0% {
    stroke-dashoffset: 0;
  }
  100% {
    stroke-dashoffset: -20;
  }
}

.edge-executing {
  stroke-dasharray: 10, 5;
  animation: flow 1s linear infinite;
  stroke: #3b82f6;
  stroke-width: 3;
  filter: drop-shadow(0 0 4px rgba(59, 130, 246, 0.5));
}
